# Batch update all project build scripts
# Change Visual Studio 16 2019 to Visual Studio 17 2022

$projectRoot = "project"
$projects = @(
    "usrzone", "usrcenter", "route", "gatetcp", "gatehttp", 
    "gameserver", "central", "placeroute", "daemon", "chat", 
    "background", "log", "sdk", "errorlog", "rdzone", "team"
)

Write-Host "Starting to update build scripts..." -ForegroundColor Green

foreach ($project in $projects) {
    $makeFile = Join-Path $projectRoot "$project\source\make_win64.bat"
    $cmakeFile = Join-Path $projectRoot "$project\source\CMakeLists.txt"
    
    if (Test-Path $makeFile) {
        Write-Host "Updating $project make_win64.bat..." -ForegroundColor Yellow
        
        # 读取文件内容
        $content = Get-Content $makeFile -Raw -Encoding UTF8
        
        # 替换Visual Studio版本
        $content = $content -replace 'Visual Studio 16 2019', 'Visual Studio 17 2022'
        
        # 确保文件开头有编码设置
        if (-not $content.StartsWith("@echo off")) {
            $content = "@echo off`r`nchcp 65001 >nul 2>&1`r`n`r`n" + $content
        } elseif (-not $content.Contains("chcp 65001")) {
            $content = $content -replace "@echo off", "@echo off`r`nchcp 65001 >nul 2>&1"
        }
        
        # 写回文件
        Set-Content $makeFile $content -Encoding UTF8
        Write-Host "  ✓ Updated make_win64.bat" -ForegroundColor Green
    } else {
        Write-Host "  ⚠ Not found $makeFile" -ForegroundColor Red
    }

    # Update minimum version requirement in CMakeLists.txt
    if (Test-Path $cmakeFile) {
        Write-Host "Updating $project CMakeLists.txt..." -ForegroundColor Yellow
        
        $content = Get-Content $cmakeFile -Raw -Encoding UTF8
        $content = $content -replace 'cmake_minimum_required \(VERSION 3\.8\)', 'cmake_minimum_required (VERSION 3.10)'
        
        Set-Content $cmakeFile $content -Encoding UTF8
        Write-Host "  ✓ Updated CMakeLists.txt" -ForegroundColor Green
    } else {
        Write-Host "  ⚠ Not found $cmakeFile" -ForegroundColor Red
    }
}

Write-Host "`nAll build scripts updated successfully!" -ForegroundColor Green
Write-Host "You can now re-run the build scripts." -ForegroundColor Cyan
