﻿@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo ========================================
echo Building All Projects (Windows x64)
echo ========================================

REM Check if Visual Studio is available
echo Checking Visual Studio installation...
cmake --help | findstr "Visual Studio 17 2022" >nul
if errorlevel 1 (
    echo ERROR: Visual Studio 17 2022 not found!
    cmake --help | findstr "Visual Studio 16 2019" >nul
    if errorlevel 1 (
        echo ERROR: No compatible Visual Studio found!
        echo Please install Visual Studio 2019 or 2022 with C++ development tools.
        pause
        exit /b 1
    ) else (
        echo WARNING: Using Visual Studio 16 2019 instead of 2022
    )
) else (
    echo SUCCESS: Visual Studio 17 2022 found
)

set args=%*
set error_count=0
set success_count=0
set total_projects=18

echo.
echo Starting build process...
echo.

echo [1/18] Building usrzone...
call make_usrzone.bat %args%
if errorlevel 1 (
    set /a error_count+=1
    echo   FAILED: usrzone build failed
) else (
    set /a success_count+=1
    echo   SUCCESS: usrzone built successfully
)

echo [2/18] Building usrcenter...
call make_usrcenter.bat %args%
if errorlevel 1 (
    set /a error_count+=1
    echo   FAILED: usrcenter build failed
) else (
    set /a success_count+=1
    echo   SUCCESS: usrcenter built successfully
)

echo [3/18] Building route...
call make_route.bat %args%
if errorlevel 1 (
    set /a error_count+=1
    echo   FAILED: route build failed
) else (
    set /a success_count+=1
    echo   SUCCESS: route built successfully
)

echo [4/18] Building gatetcp...
call make_gatetcp.bat %args%
if errorlevel 1 (
    set /a error_count+=1
    echo   FAILED: gatetcp build failed
) else (
    set /a success_count+=1
    echo   SUCCESS: gatetcp built successfully
)

echo [5/18] Building gatehttp...
call make_gatehttp.bat %args%
if errorlevel 1 (
    set /a error_count+=1
    echo   FAILED: gatehttp build failed
) else (
    set /a success_count+=1
    echo   SUCCESS: gatehttp built successfully
)

echo [6/18] Building gameserver...
call make_gameserver.bat %args%
if errorlevel 1 (
    set /a error_count+=1
    echo   FAILED: gameserver build failed
) else (
    set /a success_count+=1
    echo   SUCCESS: gameserver built successfully
)

echo [7/18] Building central...
call make_central.bat %args%
if errorlevel 1 (
    set /a error_count+=1
    echo   FAILED: central build failed
) else (
    set /a success_count+=1
    echo   SUCCESS: central built successfully
)

echo [8/18] Building placeroute...
call make_placeroute.bat %args%
if errorlevel 1 (
    set /a error_count+=1
    echo   FAILED: placeroute build failed
) else (
    set /a success_count+=1
    echo   SUCCESS: placeroute built successfully
)

echo [9/18] Building daemon...
call make_daemon.bat %args%
if errorlevel 1 (
    set /a error_count+=1
    echo   FAILED: daemon build failed
) else (
    set /a success_count+=1
    echo   SUCCESS: daemon built successfully
)

echo [10/18] Building chat...
call make_chat.bat %args%
if errorlevel 1 (
    set /a error_count+=1
    echo   FAILED: chat build failed
) else (
    set /a success_count+=1
    echo   SUCCESS: chat built successfully
)

echo [11/18] Building background...
call make_background.bat %args%
if errorlevel 1 (
    set /a error_count+=1
    echo   FAILED: background build failed
) else (
    set /a success_count+=1
    echo   SUCCESS: background built successfully
)

echo [12/18] Building log...
call make_log.bat %args%
if errorlevel 1 (
    set /a error_count+=1
    echo   FAILED: log build failed
) else (
    set /a success_count+=1
    echo   SUCCESS: log built successfully
)

echo [13/18] Building sdk...
call make_sdk.bat %args%
if errorlevel 1 (
    set /a error_count+=1
    echo   FAILED: sdk build failed
) else (
    set /a success_count+=1
    echo   SUCCESS: sdk built successfully
)

echo [14/18] Building errorlog...
call make_errorlog.bat %args%
if errorlevel 1 (
    set /a error_count+=1
    echo   FAILED: errorlog build failed
) else (
    set /a success_count+=1
    echo   SUCCESS: errorlog built successfully
)

echo [15/18] Building rdzone...
call make_rdzone.bat %args%
if errorlevel 1 (
    set /a error_count+=1
    echo   FAILED: rdzone build failed
) else (
    set /a success_count+=1
    echo   SUCCESS: rdzone built successfully
)

echo [16/18] Building team...
call make_team.bat %args%
if errorlevel 1 (
    set /a error_count+=1
    echo   FAILED: team build failed
) else (
    set /a success_count+=1
    echo   SUCCESS: team built successfully
)

echo [17/18] Building friend...
call make_friend.bat %args%
if errorlevel 1 (
    set /a error_count+=1
    echo   FAILED: friend build failed
) else (
    set /a success_count+=1
    echo   SUCCESS: friend built successfully
)

echo [18/18] Building confighttp...
call make_confighttp.bat %args%
if errorlevel 1 (
    set /a error_count+=1
    echo   FAILED: confighttp build failed
) else (
    set /a success_count+=1
    echo   SUCCESS: confighttp built successfully
)

echo.
echo ========================================
echo BUILD SUMMARY
echo ========================================
echo Total projects: %total_projects%
echo Successful builds: %success_count%
echo Failed builds: %error_count%

if %error_count% equ 0 (
    echo.
    echo BUILD SUCCESS: All projects built successfully!
    echo ========================================
) else (
    echo.
    echo BUILD FAILED: %error_count% projects failed to build!
    echo Please check the error messages above for details.
    echo ========================================
    exit /b 1
)

