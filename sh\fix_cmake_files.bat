@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo ========================================
echo 修复所有项目的 make_win64.bat 文件
echo ========================================

set CURRENT_DIR=%CD%
set error_count=0

REM 定义需要修复的项目列表
set projects=usrzone usrcenter route gatetcp gatehttp gameserver central placeroute daemon chat log sdk errorlog rdzone team friend confighttp

for %%p in (%projects%) do (
    echo [修复] %%p 项目...
    
    set project_path=project\%%p\source\make_win64.bat
    
    if exist "!project_path!" (
        echo   - 找到文件: !project_path!
        
        REM 创建临时文件
        set temp_file=!project_path!.tmp
        
        REM 处理文件内容
        (
            for /f "usebackq delims=" %%l in ("!project_path!") do (
                set "line=%%l"
                
                REM 移除多余的 ".." 参数行
                if "!line!"=="%TAB%%TAB%.." (
                    echo   - 移除多余的 ".." 参数
                ) else if "!line!"=="%TAB%%.." (
                    echo   - 移除多余的 ".." 参数
                ) else if "!line!"==".." (
                    echo   - 移除多余的 ".." 参数
                ) else (
                    REM 修复缺少空格的问题
                    set "line=!line:-DCMAKE_BUILD_TYPE=Debug^=-DCMAKE_BUILD_TYPE=Debug ^!"
                    set "line=!line:-DCMAKE_BUILD_TYPE=Release^=-DCMAKE_BUILD_TYPE=Release ^!"
                    set "line=!line:-DCMAKE_BUILD_TYPE=%BUILD_TYPE%^=-DCMAKE_BUILD_TYPE=%BUILD_TYPE% ^!"
                    
                    echo(!line!
                )
            )
        ) > "!temp_file!"
        
        REM 替换原文件
        move "!temp_file!" "!project_path!" >nul
        
        if !errorlevel! equ 0 (
            echo   - ✓ 修复成功
        ) else (
            echo   - ✗ 修复失败
            set /a error_count+=1
        )
    ) else (
        echo   - ⚠ 文件不存在: !project_path!
    )
    
    echo.
)

echo ========================================
if %error_count% equ 0 (
    echo ✓ 所有文件修复完成！
) else (
    echo ✗ 修复过程中出现 %error_count% 个错误
)
echo ========================================

pause
