﻿@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo ���� errorlog ��Ŀ...

set CURRENT_DIR=%CD%
set args=%*

cd project\errorlog\source\

if not exist "make_win64.bat" (
    echo ����: make_win64.bat �ļ������ڣ�
    cd %CURRENT_DIR%
    exit /b 1
)

call make_win64.bat %args%
set build_result=%errorlevel%

cd %CURRENT_DIR%

if %build_result% neq 0 (
    echo errorlog ����ʧ�ܣ�
    exit /b %build_result%
) else (
    echo errorlog �����ɹ���
)

