# Fix paths in all make_*.bat scripts
$scriptDir = "sh"
$makeScripts = Get-ChildItem -Path $scriptDir -Name "make_*.bat"

Write-Host "Fixing paths in make scripts..." -ForegroundColor Green

foreach ($script in $makeScripts) {
    $scriptPath = Join-Path $scriptDir $script
    Write-Host "Processing $script..." -ForegroundColor Yellow
    
    if (Test-Path $scriptPath) {
        $content = Get-Content $scriptPath -Raw -Encoding UTF8
        
        # Fix the path from ..\project\ to project\
        $content = $content -replace '\.\.\\project\\', 'project\'
        
        Set-Content $scriptPath $content -Encoding UTF8
        Write-Host "  ✓ Fixed paths in $script" -ForegroundColor Green
    }
}

Write-Host "`nAll paths fixed successfully!" -ForegroundColor Green
