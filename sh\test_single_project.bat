@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo ========================================
echo 测试单个项目构建
echo ========================================

if "%1"=="" (
    echo 用法: %0 ^<项目名称^>
    echo 例如: %0 usrzone
    echo.
    echo 可用项目:
    echo   usrzone, usrcenter, route, gatetcp, gatehttp
    echo   gameserver, central, placeroute, daemon, chat
    echo   log, sdk, errorlog, rdzone, team, friend, confighttp
    pause
    exit /b 1
)

set PROJECT_NAME=%1
set CURRENT_DIR=%CD%

echo 测试项目: %PROJECT_NAME%
echo.

REM 检查项目是否存在
if not exist "project\%PROJECT_NAME%\source\CMakeLists.txt" (
    echo 错误: 项目 %PROJECT_NAME% 的 CMakeLists.txt 不存在
    echo 路径: project\%PROJECT_NAME%\source\CMakeLists.txt
    pause
    exit /b 1
)

if not exist "project\%PROJECT_NAME%\source\make_win64.bat" (
    echo 错误: 项目 %PROJECT_NAME% 的 make_win64.bat 不存在
    echo 路径: project\%PROJECT_NAME%\source\make_win64.bat
    pause
    exit /b 1
)

echo ✓ 项目文件检查通过
echo.

REM 调用对应的构建脚本
echo 开始构建项目 %PROJECT_NAME%...
call make_%PROJECT_NAME%.bat

set build_result=%errorlevel%

echo.
echo ========================================
if %build_result% equ 0 (
    echo ✓ 项目 %PROJECT_NAME% 构建成功！
) else (
    echo ✗ 项目 %PROJECT_NAME% 构建失败！
    echo 错误代码: %build_result%
)
echo ========================================

pause
exit /b %build_result%
