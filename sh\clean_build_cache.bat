@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo ========================================
echo Cleaning Build Cache
echo ========================================

set projects=usrzone usrcenter route gatetcp gatehttp gameserver central placeroute daemon chat background log sdk errorlog rdzone team friend confighttp

for %%p in (%projects%) do (
    echo Cleaning %%p...
    if exist "project\%%p\source\build_win64" (
        echo   Removing build_win64 directory...
        rmdir /s /q "project\%%p\source\build_win64"
        echo   ✓ Cleaned %%p
    ) else (
        echo   ⚠ No build directory found for %%p
    )
)

echo.
echo ========================================
echo Build cache cleaned successfully!
echo You can now run the build scripts.
echo ========================================
