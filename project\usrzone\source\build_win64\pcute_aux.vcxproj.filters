﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\pcute_aux.dir\cmake_pch.cxx">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\src\pcute_o.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\pcute_aux.dir\Debug\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\pcute_aux.dir\Release\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\pcute_aux.dir\MinSizeRel\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\build_win64\CMakeFiles\pcute_aux.dir\RelWithDebInfo\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\BaiduNetdiskDownload\mhgd\rc\project\usrzone\source\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Precompile Header File">
      <UniqueIdentifier>{1A6A7E41-4C4C-3AEB-8353-4C2E2D6C5E09}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{6B5A894C-E71C-3371-9041-AC7F07B4C812}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
