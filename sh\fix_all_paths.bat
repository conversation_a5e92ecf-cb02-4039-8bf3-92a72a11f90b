@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo ========================================
echo 修复所有构建脚本的路径问题
echo ========================================

set projects=usrcenter gatetcp gameserver placeroute daemon chat background log sdk errorlog rdzone team friend confighttp

for %%p in (%projects%) do (
    echo [修复] make_%%p.bat...
    
    set script_file=make_%%p.bat
    
    if exist "!script_file!" (
        echo   - 找到文件: !script_file!
        
        REM 创建临时文件
        set temp_file=!script_file!.tmp
        
        REM 处理文件内容
        (
            for /f "usebackq delims=" %%l in ("!script_file!") do (
                set "line=%%l"
                
                REM 修复路径问题：将 "cd project\" 替换为 "cd ..\project\"
                set "line=!line:cd project\=cd ..\project\!"
                
                echo(!line!
            )
        ) > "!temp_file!"
        
        REM 替换原文件
        move "!temp_file!" "!script_file!" >nul
        
        if !errorlevel! equ 0 (
            echo   - ✓ 修复成功
        ) else (
            echo   - ✗ 修复失败
        )
    ) else (
        echo   - ⚠ 文件不存在: !script_file!
    )
    
    echo.
)

echo ========================================
echo ✓ 所有路径修复完成！
echo ========================================

pause
