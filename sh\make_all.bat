﻿@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo ========================================
echo ����������Ŀ (Windows x64)
echo ========================================

set args=%*
set error_count=0

echo [1/18] ���� usrzone...
call make_usrzone.bat %args%
if errorlevel 1 set /a error_count+=1

echo [2/18] ���� usrcenter...
call make_usrcenter.bat %args%
if errorlevel 1 set /a error_count+=1

echo [3/18] ���� route...
call make_route.bat %args%
if errorlevel 1 set /a error_count+=1

echo [4/18] ���� gatetcp...
call make_gatetcp.bat %args%
if errorlevel 1 set /a error_count+=1

echo [5/18] ���� gatehttp...
call make_gatehttp.bat %args%
if errorlevel 1 set /a error_count+=1

echo [6/18] ���� gameserver...
call make_gameserver.bat %args%
if errorlevel 1 set /a error_count+=1

echo [7/18] ���� central...
call make_central.bat %args%
if errorlevel 1 set /a error_count+=1

echo [8/18] ���� placeroute...
call make_placeroute.bat %args%
if errorlevel 1 set /a error_count+=1

echo [9/18] ���� daemon...
call make_daemon.bat %args%
if errorlevel 1 set /a error_count+=1

echo [10/18] ���� chat...
call make_chat.bat %args%
if errorlevel 1 set /a error_count+=1

echo [11/18] ���� background...
call make_background.bat %args%
if errorlevel 1 set /a error_count+=1

echo [12/18] ���� log...
call make_log.bat %args%
if errorlevel 1 set /a error_count+=1

echo [13/18] ���� sdk...
call make_sdk.bat %args%
if errorlevel 1 set /a error_count+=1

echo [14/18] ���� errorlog...
call make_errorlog.bat %args%
if errorlevel 1 set /a error_count+=1

echo [15/18] ���� rdzone...
call make_rdzone.bat %args%
if errorlevel 1 set /a error_count+=1

echo [16/18] ���� team...
call make_team.bat %args%
if errorlevel 1 set /a error_count+=1

echo [17/18] ���� friend...
call make_friend.bat %args%
if errorlevel 1 set /a error_count+=1

echo [18/18] ���� confighttp...
call make_confighttp.bat %args%
if errorlevel 1 set /a error_count+=1

echo ========================================
if %error_count% equ 0 (
    echo ������ɣ�������Ŀ�����ɹ���
    echo BUILD SUCCESS: All projects built successfully!
) else (
    echo ������ɣ����� %error_count% ����Ŀ����ʧ�ܣ�
    echo BUILD FAILED: %error_count% projects failed to build!
    exit /b 1
)
echo ========================================

