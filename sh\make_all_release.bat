﻿@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo ========================================
echo ����������Ŀ (Windows x64 Release)
echo ========================================

set args=%*
set error_count=0

echo [1/18] ���� usrzone (Release)...
call make_usrzone.bat Release %args%
if errorlevel 1 set /a error_count+=1

echo [2/18] ���� usrcenter (Release)...
call make_usrcenter.bat Release %args%
if errorlevel 1 set /a error_count+=1

echo [3/18] ���� route (Release)...
call make_route.bat Release %args%
if errorlevel 1 set /a error_count+=1

echo [4/18] ���� gatetcp (Release)...
call make_gatetcp.bat Release %args%
if errorlevel 1 set /a error_count+=1

echo [5/18] ���� gatehttp (Release)...
call make_gatehttp.bat Release %args%
if errorlevel 1 set /a error_count+=1

echo [6/18] ���� gameserver (Release)...
call make_gameserver.bat Release %args%
if errorlevel 1 set /a error_count+=1

echo [7/18] ���� central (Release)...
call make_central.bat Release %args%
if errorlevel 1 set /a error_count+=1

echo [8/18] ���� placeroute (Release)...
call make_placeroute.bat Release %args%
if errorlevel 1 set /a error_count+=1

echo [9/18] ���� daemon (Release)...
call make_daemon.bat Release %args%
if errorlevel 1 set /a error_count+=1

echo [10/18] ���� chat (Release)...
call make_chat.bat Release %args%
if errorlevel 1 set /a error_count+=1

echo [11/18] ���� background (Release)...
call make_background.bat Release %args%
if errorlevel 1 set /a error_count+=1

echo [12/18] ���� log (Release)...
call make_log.bat Release %args%
if errorlevel 1 set /a error_count+=1

echo [13/18] ���� sdk (Release)...
call make_sdk.bat Release %args%
if errorlevel 1 set /a error_count+=1

echo [14/18] ���� errorlog (Release)...
call make_errorlog.bat Release %args%
if errorlevel 1 set /a error_count+=1

echo [15/18] ���� rdzone (Release)...
call make_rdzone.bat Release %args%
if errorlevel 1 set /a error_count+=1

echo [16/18] ���� team (Release)...
call make_team.bat Release %args%
if errorlevel 1 set /a error_count+=1

echo [17/18] ���� friend (Release)...
call make_friend.bat Release %args%
if errorlevel 1 set /a error_count+=1

echo [18/18] ���� confighttp (Release)...
call make_confighttp.bat Release %args%
if errorlevel 1 set /a error_count+=1

echo ========================================
if %error_count% equ 0 (
    echo ������ɣ�������Ŀ�����ɹ���(Release�汾)
    echo BUILD SUCCESS: All projects built successfully! (Release)
) else (
    echo ������ɣ����� %error_count% ����Ŀ����ʧ�ܣ�(Release�汾)
    echo BUILD FAILED: %error_count% projects failed to build! (Release)
    exit /b 1
)
echo ========================================

