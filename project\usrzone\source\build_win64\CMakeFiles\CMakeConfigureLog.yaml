
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:5 (project)"
    message: |
      The system is: Windows - 10.0.19045 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
      生成启动时间为 2025/6/22 1:55:42。
      
      节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\3.31.6-msvc6\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
        已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
        正在创建目录“Debug\\CompilerIdC.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        正在对“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”执行 Touch 任务。
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\3.31.6-msvc6\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        正在删除文件“Debug\\CompilerIdC.tlog\\unsuccessfulbuild”。
        正在对“Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate”执行 Touch 任务。
      已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\3.31.6-msvc6\\CompilerIdC\\CompilerIdC.vcxproj”(默认目标)的操作。
      
      已成功生成。
          0 个警告
          0 个错误
      
      已用时间 00:00:01.95
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/3.31.6-msvc6/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
      生成启动时间为 2025/6/22 1:55:45。
      
      节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\3.31.6-msvc6\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
        已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
        正在创建目录“Debug\\CompilerIdCXX.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        正在对“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”执行 Touch 任务。
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\3.31.6-msvc6\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        正在删除文件“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”。
        正在对“Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate”执行 Touch 任务。
      已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\3.31.6-msvc6\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)的操作。
      
      已成功生成。
          0 个警告
          0 个错误
      
      已用时间 00:00:01.45
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/3.31.6-msvc6/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-vhny0u"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-vhny0u"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-vhny0u'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_19fd6.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:55:47。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-vhny0u\\cmTC_19fd6.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_19fd6.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-vhny0u\\Debug\\”。
          正在创建目录“cmTC_19fd6.dir\\Debug\\cmTC_19fd6.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_19fd6.dir\\Debug\\cmTC_19fd6.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_19fd6.dir\\Debug\\cmTC_19fd6.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_19fd6.dir\\Debug\\\\" /Fd"cmTC_19fd6.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_19fd6.dir\\Debug\\\\" /Fd"cmTC_19fd6.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-vhny0u\\Debug\\cmTC_19fd6.exe" /INCREMENTAL /ILK:"cmTC_19fd6.dir\\Debug\\cmTC_19fd6.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-vhny0u/Debug/cmTC_19fd6.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-vhny0u/Debug/cmTC_19fd6.lib" /MACHINE:X64  /machine:x64 cmTC_19fd6.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_19fd6.vcxproj -> D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-vhny0u\\Debug\\cmTC_19fd6.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_19fd6.dir\\Debug\\cmTC_19fd6.tlog\\unsuccessfulbuild”。
          正在对“cmTC_19fd6.dir\\Debug\\cmTC_19fd6.tlog\\cmTC_19fd6.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-vhny0u\\cmTC_19fd6.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:01.33
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35209.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-xj19v6"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-xj19v6"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-xj19v6'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_41c30.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:55:49。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-xj19v6\\cmTC_41c30.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_41c30.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-xj19v6\\Debug\\”。
          正在创建目录“cmTC_41c30.dir\\Debug\\cmTC_41c30.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_41c30.dir\\Debug\\cmTC_41c30.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_41c30.dir\\Debug\\cmTC_41c30.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_41c30.dir\\Debug\\\\" /Fd"cmTC_41c30.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_41c30.dir\\Debug\\\\" /Fd"cmTC_41c30.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-xj19v6\\Debug\\cmTC_41c30.exe" /INCREMENTAL /ILK:"cmTC_41c30.dir\\Debug\\cmTC_41c30.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-xj19v6/Debug/cmTC_41c30.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-xj19v6/Debug/cmTC_41c30.lib" /MACHINE:X64  /machine:x64 cmTC_41c30.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_41c30.vcxproj -> D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-xj19v6\\Debug\\cmTC_41c30.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_41c30.dir\\Debug\\cmTC_41c30.tlog\\unsuccessfulbuild”。
          正在对“cmTC_41c30.dir\\Debug\\cmTC_41c30.tlog\\cmTC_41c30.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-xj19v6\\cmTC_41c30.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:01.34
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35209.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:43 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_UNUSED_PARAMETER_MSVC"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-h9horm"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-h9horm"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_UNUSED_PARAMETER_MSVC"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-h9horm'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_43c1f.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:55:50。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-h9horm\\cmTC_43c1f.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_43c1f.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-h9horm\\Debug\\”。
          正在创建目录“cmTC_43c1f.dir\\Debug\\cmTC_43c1f.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_43c1f.dir\\Debug\\cmTC_43c1f.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_43c1f.dir\\Debug\\cmTC_43c1f.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_UNUSED_PARAMETER_MSVC /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_43c1f.dir\\Debug\\\\" /Fd"cmTC_43c1f.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4100 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-h9horm\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_UNUSED_PARAMETER_MSVC /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_43c1f.dir\\Debug\\\\" /Fd"cmTC_43c1f.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4100 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-h9horm\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-h9horm\\Debug\\cmTC_43c1f.exe" /INCREMENTAL /ILK:"cmTC_43c1f.dir\\Debug\\cmTC_43c1f.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-h9horm/Debug/cmTC_43c1f.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-h9horm/Debug/cmTC_43c1f.lib" /MACHINE:X64  /machine:x64 cmTC_43c1f.dir\\Debug\\src.obj
          cmTC_43c1f.vcxproj -> D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-h9horm\\Debug\\cmTC_43c1f.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_43c1f.dir\\Debug\\cmTC_43c1f.tlog\\unsuccessfulbuild”。
          正在对“cmTC_43c1f.dir\\Debug\\cmTC_43c1f.tlog\\cmTC_43c1f.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-h9horm\\cmTC_43c1f.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:01.34
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:44 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_CONDITIONAL_CONSTANT_MSVC"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-61v1en"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-61v1en"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_CONDITIONAL_CONSTANT_MSVC"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-61v1en'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_8b46b.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:55:52。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-61v1en\\cmTC_8b46b.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_8b46b.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-61v1en\\Debug\\”。
          正在创建目录“cmTC_8b46b.dir\\Debug\\cmTC_8b46b.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_8b46b.dir\\Debug\\cmTC_8b46b.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_8b46b.dir\\Debug\\cmTC_8b46b.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_CONDITIONAL_CONSTANT_MSVC /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_8b46b.dir\\Debug\\\\" /Fd"cmTC_8b46b.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4127 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-61v1en\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_CONDITIONAL_CONSTANT_MSVC /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_8b46b.dir\\Debug\\\\" /Fd"cmTC_8b46b.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4127 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-61v1en\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-61v1en\\Debug\\cmTC_8b46b.exe" /INCREMENTAL /ILK:"cmTC_8b46b.dir\\Debug\\cmTC_8b46b.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-61v1en/Debug/cmTC_8b46b.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-61v1en/Debug/cmTC_8b46b.lib" /MACHINE:X64  /machine:x64 cmTC_8b46b.dir\\Debug\\src.obj
          cmTC_8b46b.vcxproj -> D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-61v1en\\Debug\\cmTC_8b46b.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_8b46b.dir\\Debug\\cmTC_8b46b.tlog\\unsuccessfulbuild”。
          正在对“cmTC_8b46b.dir\\Debug\\cmTC_8b46b.tlog\\cmTC_8b46b.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-61v1en\\cmTC_8b46b.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:01.36
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:45 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_NONSTANDARD_MSVC"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-87gm9i"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-87gm9i"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_NONSTANDARD_MSVC"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-87gm9i'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_39b2f.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:55:54。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-87gm9i\\cmTC_39b2f.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_39b2f.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-87gm9i\\Debug\\”。
          正在创建目录“cmTC_39b2f.dir\\Debug\\cmTC_39b2f.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_39b2f.dir\\Debug\\cmTC_39b2f.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_39b2f.dir\\Debug\\cmTC_39b2f.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_NONSTANDARD_MSVC /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_39b2f.dir\\Debug\\\\" /Fd"cmTC_39b2f.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4201 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-87gm9i\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_NONSTANDARD_MSVC /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_39b2f.dir\\Debug\\\\" /Fd"cmTC_39b2f.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4201 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-87gm9i\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-87gm9i\\Debug\\cmTC_39b2f.exe" /INCREMENTAL /ILK:"cmTC_39b2f.dir\\Debug\\cmTC_39b2f.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-87gm9i/Debug/cmTC_39b2f.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-87gm9i/Debug/cmTC_39b2f.lib" /MACHINE:X64  /machine:x64 cmTC_39b2f.dir\\Debug\\src.obj
          cmTC_39b2f.vcxproj -> D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-87gm9i\\Debug\\cmTC_39b2f.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_39b2f.dir\\Debug\\cmTC_39b2f.tlog\\unsuccessfulbuild”。
          正在对“cmTC_39b2f.dir\\Debug\\cmTC_39b2f.tlog\\cmTC_39b2f.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-87gm9i\\cmTC_39b2f.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:01.31
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:46 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_NONSTANDARD_EMPTY_TU_MSVC"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-knzbev"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-knzbev"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_NONSTANDARD_EMPTY_TU_MSVC"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-knzbev'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_3e2b3.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:55:55。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-knzbev\\cmTC_3e2b3.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_3e2b3.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-knzbev\\Debug\\”。
          正在创建目录“cmTC_3e2b3.dir\\Debug\\cmTC_3e2b3.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_3e2b3.dir\\Debug\\cmTC_3e2b3.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_3e2b3.dir\\Debug\\cmTC_3e2b3.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_NONSTANDARD_EMPTY_TU_MSVC /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_3e2b3.dir\\Debug\\\\" /Fd"cmTC_3e2b3.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4206 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-knzbev\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_NONSTANDARD_EMPTY_TU_MSVC /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_3e2b3.dir\\Debug\\\\" /Fd"cmTC_3e2b3.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4206 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-knzbev\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-knzbev\\Debug\\cmTC_3e2b3.exe" /INCREMENTAL /ILK:"cmTC_3e2b3.dir\\Debug\\cmTC_3e2b3.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-knzbev/Debug/cmTC_3e2b3.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-knzbev/Debug/cmTC_3e2b3.lib" /MACHINE:X64  /machine:x64 cmTC_3e2b3.dir\\Debug\\src.obj
          cmTC_3e2b3.vcxproj -> D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-knzbev\\Debug\\cmTC_3e2b3.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_3e2b3.dir\\Debug\\cmTC_3e2b3.tlog\\unsuccessfulbuild”。
          正在对“cmTC_3e2b3.dir\\Debug\\cmTC_3e2b3.tlog\\cmTC_3e2b3.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-knzbev\\cmTC_3e2b3.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:01.36
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:47 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_NONSTANDARD_FILE_SCOPE_MSVC"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-lmfrtt"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-lmfrtt"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_NONSTANDARD_FILE_SCOPE_MSVC"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-lmfrtt'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_de3fb.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:55:57。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-lmfrtt\\cmTC_de3fb.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_de3fb.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-lmfrtt\\Debug\\”。
          正在创建目录“cmTC_de3fb.dir\\Debug\\cmTC_de3fb.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_de3fb.dir\\Debug\\cmTC_de3fb.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_de3fb.dir\\Debug\\cmTC_de3fb.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_NONSTANDARD_FILE_SCOPE_MSVC /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_de3fb.dir\\Debug\\\\" /Fd"cmTC_de3fb.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4210 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-lmfrtt\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_NONSTANDARD_FILE_SCOPE_MSVC /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_de3fb.dir\\Debug\\\\" /Fd"cmTC_de3fb.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4210 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-lmfrtt\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-lmfrtt\\Debug\\cmTC_de3fb.exe" /INCREMENTAL /ILK:"cmTC_de3fb.dir\\Debug\\cmTC_de3fb.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-lmfrtt/Debug/cmTC_de3fb.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-lmfrtt/Debug/cmTC_de3fb.lib" /MACHINE:X64  /machine:x64 cmTC_de3fb.dir\\Debug\\src.obj
          cmTC_de3fb.vcxproj -> D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-lmfrtt\\Debug\\cmTC_de3fb.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_de3fb.dir\\Debug\\cmTC_de3fb.tlog\\unsuccessfulbuild”。
          正在对“cmTC_de3fb.dir\\Debug\\cmTC_de3fb.tlog\\cmTC_de3fb.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-lmfrtt\\cmTC_de3fb.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:01.35
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:48 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_NONSTANDARD_NONSTATIC_DLIMPORT_MSVC"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-c8jjui"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-c8jjui"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_NONSTANDARD_NONSTATIC_DLIMPORT_MSVC"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-c8jjui'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_1eef1.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:55:59。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-c8jjui\\cmTC_1eef1.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_1eef1.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-c8jjui\\Debug\\”。
          正在创建目录“cmTC_1eef1.dir\\Debug\\cmTC_1eef1.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_1eef1.dir\\Debug\\cmTC_1eef1.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_1eef1.dir\\Debug\\cmTC_1eef1.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_NONSTANDARD_NONSTATIC_DLIMPORT_MSVC /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_1eef1.dir\\Debug\\\\" /Fd"cmTC_1eef1.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4232 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-c8jjui\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_NONSTANDARD_NONSTATIC_DLIMPORT_MSVC /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_1eef1.dir\\Debug\\\\" /Fd"cmTC_1eef1.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4232 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-c8jjui\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-c8jjui\\Debug\\cmTC_1eef1.exe" /INCREMENTAL /ILK:"cmTC_1eef1.dir\\Debug\\cmTC_1eef1.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-c8jjui/Debug/cmTC_1eef1.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-c8jjui/Debug/cmTC_1eef1.lib" /MACHINE:X64  /machine:x64 cmTC_1eef1.dir\\Debug\\src.obj
          cmTC_1eef1.vcxproj -> D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-c8jjui\\Debug\\cmTC_1eef1.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_1eef1.dir\\Debug\\cmTC_1eef1.tlog\\unsuccessfulbuild”。
          正在对“cmTC_1eef1.dir\\Debug\\cmTC_1eef1.tlog\\cmTC_1eef1.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-c8jjui\\cmTC_1eef1.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:01.34
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:49 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_HIDES_LOCAL"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-c1wpt7"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-c1wpt7"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_HIDES_LOCAL"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-c1wpt7'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_51f82.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:56:00。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-c1wpt7\\cmTC_51f82.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_51f82.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-c1wpt7\\Debug\\”。
          正在创建目录“cmTC_51f82.dir\\Debug\\cmTC_51f82.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_51f82.dir\\Debug\\cmTC_51f82.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_51f82.dir\\Debug\\cmTC_51f82.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_HIDES_LOCAL /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_51f82.dir\\Debug\\\\" /Fd"cmTC_51f82.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4456 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-c1wpt7\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_HIDES_LOCAL /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_51f82.dir\\Debug\\\\" /Fd"cmTC_51f82.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4456 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-c1wpt7\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-c1wpt7\\Debug\\cmTC_51f82.exe" /INCREMENTAL /ILK:"cmTC_51f82.dir\\Debug\\cmTC_51f82.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-c1wpt7/Debug/cmTC_51f82.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-c1wpt7/Debug/cmTC_51f82.lib" /MACHINE:X64  /machine:x64 cmTC_51f82.dir\\Debug\\src.obj
          cmTC_51f82.vcxproj -> D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-c1wpt7\\Debug\\cmTC_51f82.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_51f82.dir\\Debug\\cmTC_51f82.tlog\\unsuccessfulbuild”。
          正在对“cmTC_51f82.dir\\Debug\\cmTC_51f82.tlog\\cmTC_51f82.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-c1wpt7\\cmTC_51f82.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:01.34
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:50 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_HIDES_PARAM"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-thba4z"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-thba4z"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_HIDES_PARAM"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-thba4z'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_1bd53.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:56:02。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-thba4z\\cmTC_1bd53.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_1bd53.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-thba4z\\Debug\\”。
          正在创建目录“cmTC_1bd53.dir\\Debug\\cmTC_1bd53.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_1bd53.dir\\Debug\\cmTC_1bd53.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_1bd53.dir\\Debug\\cmTC_1bd53.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_HIDES_PARAM /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_1bd53.dir\\Debug\\\\" /Fd"cmTC_1bd53.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4457 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-thba4z\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_HIDES_PARAM /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_1bd53.dir\\Debug\\\\" /Fd"cmTC_1bd53.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4457 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-thba4z\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-thba4z\\Debug\\cmTC_1bd53.exe" /INCREMENTAL /ILK:"cmTC_1bd53.dir\\Debug\\cmTC_1bd53.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-thba4z/Debug/cmTC_1bd53.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-thba4z/Debug/cmTC_1bd53.lib" /MACHINE:X64  /machine:x64 cmTC_1bd53.dir\\Debug\\src.obj
          cmTC_1bd53.vcxproj -> D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-thba4z\\Debug\\cmTC_1bd53.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_1bd53.dir\\Debug\\cmTC_1bd53.tlog\\unsuccessfulbuild”。
          正在对“cmTC_1bd53.dir\\Debug\\cmTC_1bd53.tlog\\cmTC_1bd53.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-thba4z\\cmTC_1bd53.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:01.35
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:51 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_HIDES_GLOBAL"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-lscsvw"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-lscsvw"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_HIDES_GLOBAL"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-lscsvw'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_dbb45.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:56:04。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-lscsvw\\cmTC_dbb45.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_dbb45.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-lscsvw\\Debug\\”。
          正在创建目录“cmTC_dbb45.dir\\Debug\\cmTC_dbb45.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_dbb45.dir\\Debug\\cmTC_dbb45.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_dbb45.dir\\Debug\\cmTC_dbb45.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_HIDES_GLOBAL /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_dbb45.dir\\Debug\\\\" /Fd"cmTC_dbb45.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4459 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-lscsvw\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_HIDES_GLOBAL /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_dbb45.dir\\Debug\\\\" /Fd"cmTC_dbb45.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4459 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-lscsvw\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-lscsvw\\Debug\\cmTC_dbb45.exe" /INCREMENTAL /ILK:"cmTC_dbb45.dir\\Debug\\cmTC_dbb45.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-lscsvw/Debug/cmTC_dbb45.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-lscsvw/Debug/cmTC_dbb45.lib" /MACHINE:X64  /machine:x64 cmTC_dbb45.dir\\Debug\\src.obj
          cmTC_dbb45.vcxproj -> D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-lscsvw\\Debug\\cmTC_dbb45.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_dbb45.dir\\Debug\\cmTC_dbb45.tlog\\unsuccessfulbuild”。
          正在对“cmTC_dbb45.dir\\Debug\\cmTC_dbb45.tlog\\cmTC_dbb45.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-lscsvw\\cmTC_dbb45.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:01.34
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:52 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_CONDITIONAL_ASSIGNMENT_MSVC"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-kpb2pu"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-kpb2pu"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_CONDITIONAL_ASSIGNMENT_MSVC"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-kpb2pu'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_9a31d.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:56:05。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-kpb2pu\\cmTC_9a31d.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_9a31d.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-kpb2pu\\Debug\\”。
          正在创建目录“cmTC_9a31d.dir\\Debug\\cmTC_9a31d.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_9a31d.dir\\Debug\\cmTC_9a31d.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_9a31d.dir\\Debug\\cmTC_9a31d.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_CONDITIONAL_ASSIGNMENT_MSVC /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_9a31d.dir\\Debug\\\\" /Fd"cmTC_9a31d.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4706 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-kpb2pu\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_CONDITIONAL_ASSIGNMENT_MSVC /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_9a31d.dir\\Debug\\\\" /Fd"cmTC_9a31d.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4706 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-kpb2pu\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-kpb2pu\\Debug\\cmTC_9a31d.exe" /INCREMENTAL /ILK:"cmTC_9a31d.dir\\Debug\\cmTC_9a31d.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-kpb2pu/Debug/cmTC_9a31d.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-kpb2pu/Debug/cmTC_9a31d.lib" /MACHINE:X64  /machine:x64 cmTC_9a31d.dir\\Debug\\src.obj
          cmTC_9a31d.vcxproj -> D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-kpb2pu\\Debug\\cmTC_9a31d.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_9a31d.dir\\Debug\\cmTC_9a31d.tlog\\unsuccessfulbuild”。
          正在对“cmTC_9a31d.dir\\Debug\\cmTC_9a31d.tlog\\cmTC_9a31d.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-kpb2pu\\cmTC_9a31d.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:01.32
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:53 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_UNSAFE_MSVC"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-2jpxrk"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-2jpxrk"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_UNSAFE_MSVC"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-2jpxrk'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_bbfb0.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:56:07。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-2jpxrk\\cmTC_bbfb0.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_bbfb0.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-2jpxrk\\Debug\\”。
          正在创建目录“cmTC_bbfb0.dir\\Debug\\cmTC_bbfb0.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_bbfb0.dir\\Debug\\cmTC_bbfb0.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_bbfb0.dir\\Debug\\cmTC_bbfb0.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_UNSAFE_MSVC /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_bbfb0.dir\\Debug\\\\" /Fd"cmTC_bbfb0.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4996 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-2jpxrk\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_UNSAFE_MSVC /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_bbfb0.dir\\Debug\\\\" /Fd"cmTC_bbfb0.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4996 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-2jpxrk\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-2jpxrk\\Debug\\cmTC_bbfb0.exe" /INCREMENTAL /ILK:"cmTC_bbfb0.dir\\Debug\\cmTC_bbfb0.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-2jpxrk/Debug/cmTC_bbfb0.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-2jpxrk/Debug/cmTC_bbfb0.lib" /MACHINE:X64  /machine:x64 cmTC_bbfb0.dir\\Debug\\src.obj
          cmTC_bbfb0.vcxproj -> D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-2jpxrk\\Debug\\cmTC_bbfb0.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_bbfb0.dir\\Debug\\cmTC_bbfb0.tlog\\unsuccessfulbuild”。
          正在对“cmTC_bbfb0.dir\\Debug\\cmTC_bbfb0.tlog\\cmTC_bbfb0.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-2jpxrk\\cmTC_bbfb0.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:01.31
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:54 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_WRONG_CODE"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-45g6b4"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-45g6b4"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_WRONG_CODE"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-45g6b4'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_7013c.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:56:09。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-45g6b4\\cmTC_7013c.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_7013c.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-45g6b4\\Debug\\”。
          正在创建目录“cmTC_7013c.dir\\Debug\\cmTC_7013c.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_7013c.dir\\Debug\\cmTC_7013c.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_7013c.dir\\Debug\\cmTC_7013c.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_WRONG_CODE /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_7013c.dir\\Debug\\\\" /Fd"cmTC_7013c.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4819 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-45g6b4\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_WRONG_CODE /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_7013c.dir\\Debug\\\\" /Fd"cmTC_7013c.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /wd4819 /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-45g6b4\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-45g6b4\\Debug\\cmTC_7013c.exe" /INCREMENTAL /ILK:"cmTC_7013c.dir\\Debug\\cmTC_7013c.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-45g6b4/Debug/cmTC_7013c.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-45g6b4/Debug/cmTC_7013c.lib" /MACHINE:X64  /machine:x64 cmTC_7013c.dir\\Debug\\src.obj
          cmTC_7013c.vcxproj -> D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-45g6b4\\Debug\\cmTC_7013c.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_7013c.dir\\Debug\\cmTC_7013c.tlog\\unsuccessfulbuild”。
          正在对“cmTC_7013c.dir\\Debug\\cmTC_7013c.tlog\\cmTC_7013c.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-45g6b4\\cmTC_7013c.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:01.32
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:56 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_WALL"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-eszeur"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-eszeur"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/cmake"
    buildResult:
      variable: "LC_LINT_WALL"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-eszeur'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_79898.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:56:10。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-eszeur\\cmTC_79898.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_79898.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-eszeur\\Debug\\”。
          正在创建目录“cmTC_79898.dir\\Debug\\cmTC_79898.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_79898.dir\\Debug\\cmTC_79898.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_79898.dir\\Debug\\cmTC_79898.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /Wall /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_WALL /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_79898.dir\\Debug\\\\" /Fd"cmTC_79898.dir\\Debug\\vc143.pdb" /external:W4 /Gd /TC /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-eszeur\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /Wall /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_WALL /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_79898.dir\\Debug\\\\" /Fd"cmTC_79898.dir\\Debug\\vc143.pdb" /external:W4 /Gd /TC /errorReport:queue "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-eszeur\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-eszeur\\Debug\\cmTC_79898.exe" /INCREMENTAL /ILK:"cmTC_79898.dir\\Debug\\cmTC_79898.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-eszeur/Debug/cmTC_79898.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-eszeur/Debug/cmTC_79898.lib" /MACHINE:X64  /machine:x64 cmTC_79898.dir\\Debug\\src.obj
          cmTC_79898.vcxproj -> D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-eszeur\\Debug\\cmTC_79898.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_79898.dir\\Debug\\cmTC_79898.tlog\\unsuccessfulbuild”。
          正在对“cmTC_79898.dir\\Debug\\cmTC_79898.tlog\\cmTC_79898.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-eszeur\\cmTC_79898.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:01.40
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:58 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_NO_UNUSED_PARAMETER"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-dvaaz4"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-dvaaz4"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/cmake"
    buildResult:
      variable: "LC_LINT_NO_UNUSED_PARAMETER"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-dvaaz4'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_c2557.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:56:12。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-dvaaz4\\cmTC_c2557.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_c2557.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-dvaaz4\\Debug\\”。
          正在创建目录“cmTC_c2557.dir\\Debug\\cmTC_c2557.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_c2557.dir\\Debug\\cmTC_c2557.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_c2557.dir\\Debug\\cmTC_c2557.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_UNUSED_PARAMETER /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_c2557.dir\\Debug\\\\" /Fd"cmTC_c2557.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue  -Wno-unused-parameter "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-dvaaz4\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_NO_UNUSED_PARAMETER /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_c2557.dir\\Debug\\\\" /Fd"cmTC_c2557.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue  -Wno-unused-parameter "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-dvaaz4\\src.c"
        cl : 命令行  error D8021: 无效的数值参数“/Wno-unused-parameter” [D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-dvaaz4\\cmTC_c2557.vcxproj]
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-dvaaz4\\cmTC_c2557.vcxproj”(默认目标)的操作 - 失败。
        
        生成失败。
        
        “D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-dvaaz4\\cmTC_c2557.vcxproj”(默认目标) (1) ->
        (ClCompile 目标) -> 
          cl : 命令行  error D8021: 无效的数值参数“/Wno-unused-parameter” [D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-dvaaz4\\cmTC_c2557.vcxproj]
        
            0 个警告
            1 个错误
        
        已用时间 00:00:00.72
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:59 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_STRICT_PROTOTYPES"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-zc1adl"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-zc1adl"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/cmake"
    buildResult:
      variable: "LC_LINT_STRICT_PROTOTYPES"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-zc1adl'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_5fcf6.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:56:13。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-zc1adl\\cmTC_5fcf6.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_5fcf6.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-zc1adl\\Debug\\”。
          正在创建目录“cmTC_5fcf6.dir\\Debug\\cmTC_5fcf6.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_5fcf6.dir\\Debug\\cmTC_5fcf6.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_5fcf6.dir\\Debug\\cmTC_5fcf6.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_STRICT_PROTOTYPES /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_5fcf6.dir\\Debug\\\\" /Fd"cmTC_5fcf6.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue  -Wstrict-prototypes "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-zc1adl\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_STRICT_PROTOTYPES /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_5fcf6.dir\\Debug\\\\" /Fd"cmTC_5fcf6.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue  -Wstrict-prototypes "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-zc1adl\\src.c"
        cl : 命令行  error D8021: 无效的数值参数“/Wstrict-prototypes” [D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-zc1adl\\cmTC_5fcf6.vcxproj]
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-zc1adl\\cmTC_5fcf6.vcxproj”(默认目标)的操作 - 失败。
        
        生成失败。
        
        “D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-zc1adl\\cmTC_5fcf6.vcxproj”(默认目标) (1) ->
        (ClCompile 目标) -> 
          cl : 命令行  error D8021: 无效的数值参数“/Wstrict-prototypes” [D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-zc1adl\\cmTC_5fcf6.vcxproj]
        
            0 个警告
            1 个错误
        
        已用时间 00:00:00.68
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:60 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_EXTRA"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-cpi8jm"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-cpi8jm"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/cmake"
    buildResult:
      variable: "LC_LINT_EXTRA"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-cpi8jm'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_9ddeb.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:56:14。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-cpi8jm\\cmTC_9ddeb.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_9ddeb.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-cpi8jm\\Debug\\”。
          正在创建目录“cmTC_9ddeb.dir\\Debug\\cmTC_9ddeb.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_9ddeb.dir\\Debug\\cmTC_9ddeb.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_9ddeb.dir\\Debug\\cmTC_9ddeb.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_EXTRA /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_9ddeb.dir\\Debug\\\\" /Fd"cmTC_9ddeb.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue  -Wextra "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-cpi8jm\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_EXTRA /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_9ddeb.dir\\Debug\\\\" /Fd"cmTC_9ddeb.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue  -Wextra "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-cpi8jm\\src.c"
        cl : 命令行  error D8021: 无效的数值参数“/Wextra” [D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-cpi8jm\\cmTC_9ddeb.vcxproj]
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-cpi8jm\\cmTC_9ddeb.vcxproj”(默认目标)的操作 - 失败。
        
        生成失败。
        
        “D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-cpi8jm\\cmTC_9ddeb.vcxproj”(默认目标) (1) ->
        (ClCompile 目标) -> 
          cl : 命令行  error D8021: 无效的数值参数“/Wextra” [D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-cpi8jm\\cmTC_9ddeb.vcxproj]
        
            0 个警告
            1 个错误
        
        已用时间 00:00:00.74
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:62 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_BUILTIN_REDEFINE"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-ko66ln"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-ko66ln"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/cmake"
    buildResult:
      variable: "LC_LINT_BUILTIN_REDEFINE"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-ko66ln'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_27579.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:56:15。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-ko66ln\\cmTC_27579.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_27579.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-ko66ln\\Debug\\”。
          正在创建目录“cmTC_27579.dir\\Debug\\cmTC_27579.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_27579.dir\\Debug\\cmTC_27579.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_27579.dir\\Debug\\cmTC_27579.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_BUILTIN_REDEFINE /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_27579.dir\\Debug\\\\" /Fd"cmTC_27579.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue  -Wno-builtin-macro-redefined "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-ko66ln\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_BUILTIN_REDEFINE /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_27579.dir\\Debug\\\\" /Fd"cmTC_27579.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue  -Wno-builtin-macro-redefined "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-ko66ln\\src.c"
        cl : 命令行  error D8021: 无效的数值参数“/Wno-builtin-macro-redefined” [D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-ko66ln\\cmTC_27579.vcxproj]
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-ko66ln\\cmTC_27579.vcxproj”(默认目标)的操作 - 失败。
        
        生成失败。
        
        “D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-ko66ln\\cmTC_27579.vcxproj”(默认目标) (1) ->
        (ClCompile 目标) -> 
          cl : 命令行  error D8021: 无效的数值参数“/Wno-builtin-macro-redefined” [D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-ko66ln\\cmTC_27579.vcxproj]
        
            0 个警告
            1 个错误
        
        已用时间 00:00:00.73
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:65 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_LINT_UTF8_MSVC"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-un7wcn"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-un7wcn"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/cmake"
    buildResult:
      variable: "LC_LINT_UTF8_MSVC"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-un7wcn'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_3f9fd.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:56:16。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-un7wcn\\cmTC_3f9fd.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_3f9fd.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-un7wcn\\Debug\\”。
          正在创建目录“cmTC_3f9fd.dir\\Debug\\cmTC_3f9fd.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_3f9fd.dir\\Debug\\cmTC_3f9fd.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_3f9fd.dir\\Debug\\cmTC_3f9fd.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_UTF8_MSVC /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_3f9fd.dir\\Debug\\\\" /Fd"cmTC_3f9fd.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue  /utf-8 "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-un7wcn\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_LINT_UTF8_MSVC /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_3f9fd.dir\\Debug\\\\" /Fd"cmTC_3f9fd.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue  /utf-8 "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-un7wcn\\src.c"
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-un7wcn\\Debug\\cmTC_3f9fd.exe" /INCREMENTAL /ILK:"cmTC_3f9fd.dir\\Debug\\cmTC_3f9fd.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-un7wcn/Debug/cmTC_3f9fd.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-un7wcn/Debug/cmTC_3f9fd.lib" /MACHINE:X64  /machine:x64 cmTC_3f9fd.dir\\Debug\\src.obj
          cmTC_3f9fd.vcxproj -> D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-un7wcn\\Debug\\cmTC_3f9fd.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_3f9fd.dir\\Debug\\cmTC_3f9fd.tlog\\unsuccessfulbuild”。
          正在对“cmTC_3f9fd.dir\\Debug\\cmTC_3f9fd.tlog\\cmTC_3f9fd.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-un7wcn\\cmTC_3f9fd.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:01.37
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCCompilerFlag.cmake:56 (cmake_check_compiler_flag)"
      - "CMakeLists.txt:113 (check_c_compiler_flag)"
    checks:
      - "Performing Test LC_F_STRICT_ALIASING"
    directories:
      source: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-vr5gbx"
      binary: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-vr5gbx"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/cmake"
    buildResult:
      variable: "LC_F_STRICT_ALIASING"
      cached: true
      stdout: |
        Change Dir: 'D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-vr5gbx'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_95eb5.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        适用于 .NET Framework MSBuild 版本 17.14.10+8b8e13593
        生成启动时间为 2025/6/22 1:56:18。
        
        节点 1 上的项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-vr5gbx\\cmTC_95eb5.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_95eb5.dir\\Debug\\”。
          已启用结构化输出。编译器诊断的格式设置将反映错误层次结构。有关详细信息，请参阅 https://aka.ms/cpp/structured-output。
          正在创建目录“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-vr5gbx\\Debug\\”。
          正在创建目录“cmTC_95eb5.dir\\Debug\\cmTC_95eb5.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_95eb5.dir\\Debug\\cmTC_95eb5.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
          正在对“cmTC_95eb5.dir\\Debug\\cmTC_95eb5.tlog\\unsuccessfulbuild”执行 Touch 任务。
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_F_STRICT_ALIASING /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_95eb5.dir\\Debug\\\\" /Fd"cmTC_95eb5.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue  -fno-strict-aliasing "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-vr5gbx\\src.c"
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.44.35209 版
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D LC_F_STRICT_ALIASING /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_95eb5.dir\\Debug\\\\" /Fd"cmTC_95eb5.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue  -fno-strict-aliasing "D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-vr5gbx\\src.c"
        cl : 命令行  warning D9002: 忽略未知选项“-fno-strict-aliasing” [D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-vr5gbx\\cmTC_95eb5.vcxproj]
          src.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-vr5gbx\\Debug\\cmTC_95eb5.exe" /INCREMENTAL /ILK:"cmTC_95eb5.dir\\Debug\\cmTC_95eb5.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-vr5gbx/Debug/cmTC_95eb5.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/CMakeScratch/TryCompile-vr5gbx/Debug/cmTC_95eb5.lib" /MACHINE:X64  /machine:x64 cmTC_95eb5.dir\\Debug\\src.obj
          cmTC_95eb5.vcxproj -> D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-vr5gbx\\Debug\\cmTC_95eb5.exe
        FinalizeBuildStatus:
          正在删除文件“cmTC_95eb5.dir\\Debug\\cmTC_95eb5.tlog\\unsuccessfulbuild”。
          正在对“cmTC_95eb5.dir\\Debug\\cmTC_95eb5.tlog\\cmTC_95eb5.lastbuildstate”执行 Touch 任务。
        已完成生成项目“D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-vr5gbx\\cmTC_95eb5.vcxproj”(默认目标)的操作。
        
        已成功生成。
        
        “D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-vr5gbx\\cmTC_95eb5.vcxproj”(默认目标) (1) ->
        (ClCompile 目标) -> 
          cl : 命令行  warning D9002: 忽略未知选项“-fno-strict-aliasing” [D:\\BaiduNetdiskDownload\\mhgd\\rc\\project\\usrzone\\source\\build_win64\\CMakeFiles\\CMakeScratch\\TryCompile-vr5gbx\\cmTC_95eb5.vcxproj]
        
            1 个警告
            0 个错误
        
        已用时间 00:00:01.34
        
      exitCode: 0
...
