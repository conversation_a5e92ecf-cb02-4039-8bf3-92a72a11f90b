﻿#cmake_tools.cmake

cmake_minimum_required (VERSION 3.10)

project ("project_background")

list(APPEND CMAKE_MODULE_PATH "${PROJECT_SOURCE_DIR}/cmake")

set(BUILD_DEBUG 	0)
set(BUILD_RELEASE 	0)

if(${CMAKE_BUILD_TYPE} STREQUAL "Debug")
	set(BUILD_DEBUG 1)
else()
	set(BUILD_RELEASE 1)
endif()

option(MAKE_INSTALL 	"run install process" 	OFF)
option(BUILD_TESTING 	"run testing" 			OFF)
include(CTest)


include(CMakePackageConfigHelpers)
include(CMakeDependentOption)
include(CheckCCompilerFlag)
include(GNUInstallDirs)

set(CMAKE_C_VISIBILITY_PRESET hidden)
set(CMAKE_C_STANDARD_REQUIRED ON)
set(CMAKE_C_EXTENSIONS ON)
set(CMAKE_C_STANDARD 99)

set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_STANDARD 17)

# Compiler check
string(CONCAT is-msvc $<OR:
	$<C_COMPILER_ID:MSVC>,
	$<STREQUAL:${CMAKE_C_COMPILER_FRONTEND_VARIANT},MSVC>
>)

# check_c_compiler_flag(/W4 			LC_LINT_W4)
check_c_compiler_flag(/wd4100 	LC_LINT_NO_UNUSED_PARAMETER_MSVC)
check_c_compiler_flag(/wd4127 	LC_LINT_NO_CONDITIONAL_CONSTANT_MSVC)
check_c_compiler_flag(/wd4201 	LC_LINT_NO_NONSTANDARD_MSVC)
check_c_compiler_flag(/wd4206 	LC_LINT_NO_NONSTANDARD_EMPTY_TU_MSVC)
check_c_compiler_flag(/wd4210 	LC_LINT_NO_NONSTANDARD_FILE_SCOPE_MSVC)
check_c_compiler_flag(/wd4232 	LC_LINT_NO_NONSTANDARD_NONSTATIC_DLIMPORT_MSVC)
check_c_compiler_flag(/wd4456 	LC_LINT_NO_HIDES_LOCAL)
check_c_compiler_flag(/wd4457 	LC_LINT_NO_HIDES_PARAM)
check_c_compiler_flag(/wd4459 	LC_LINT_NO_HIDES_GLOBAL)
check_c_compiler_flag(/wd4706 	LC_LINT_NO_CONDITIONAL_ASSIGNMENT_MSVC)
check_c_compiler_flag(/wd4996 	LC_LINT_NO_UNSAFE_MSVC)
check_c_compiler_flag(/wd4819 	LC_LINT_NO_WRONG_CODE)

check_c_compiler_flag(-Wall 	LC_LINT_WALL) # DO NOT use this under MSVC

check_c_compiler_flag(-Wno-unused-parameter LC_LINT_NO_UNUSED_PARAMETER)
check_c_compiler_flag(-Wstrict-prototypes 	LC_LINT_STRICT_PROTOTYPES)
check_c_compiler_flag(-Wextra 				LC_LINT_EXTRA)

check_c_compiler_flag(-Wno-builtin-macro-redefined LC_LINT_BUILTIN_REDEFINE)


check_c_compiler_flag(/utf-8 LC_LINT_UTF8_MSVC)

set(lint-no-unused-parameter $<$<BOOL:${LC_LINT_NO_UNUSED_PARAMETER}>:-Wno-unused-parameter>)
set(lint-strict-prototypes $<$<BOOL:${LC_LINT_STRICT_PROTOTYPES}>:-Wstrict-prototypes>)
set(lint-extra $<$<BOOL:${LC_LINT_EXTRA}>:-Wextra>)
set(lint-no-unused-parameter-msvc $<$<BOOL:${LC_LINT_NO_UNUSED_PARAMETER_MSVC}>:/wd4100>)
set(lint-no-conditional-constant-msvc $<$<BOOL:${LC_LINT_NO_CONDITIONAL_CONSTANT_MSVC}>:/wd4127>)
set(lint-no-nonstandard-msvc $<$<BOOL:${LC_LINT_NO_NONSTANDARD_MSVC}>:/wd4201>)
set(lint-no-nonstandard-empty-tu-msvc $<$<BOOL:${LC_LINT_NO_NONSTANDARD_EMPTY_TU_MSVC}>:/wd4206>)
set(lint-no-nonstandard-file-scope-msvc $<$<BOOL:${LC_LINT_NO_NONSTANDARD_FILE_SCOPE_MSVC}>:/wd4210>)
set(lint-no-nonstandard-nonstatic-dlimport-msvc $<$<BOOL:${LC_LINT_NO_NONSTANDARD_NONSTATIC_DLIMPORT_MSVC}>:/wd4232>)
set(lint-no-hides-local-msvc $<$<BOOL:${LC_LINT_NO_HIDES_LOCAL}>:/wd4456>)
set(lint-no-hides-param-msvc $<$<BOOL:${LC_LINT_NO_HIDES_PARAM}>:/wd4457>)
set(lint-no-hides-global-msvc $<$<BOOL:${LC_LINT_NO_HIDES_GLOBAL}>:/wd4459>)
set(lint-no-conditional-assignment-msvc $<$<BOOL:${LC_LINT_NO_CONDITIONAL_ASSIGNMENT_MSVC}>:/wd4706>)
set(lint-no-unsafe-msvc $<$<BOOL:${LC_LINT_NO_UNSAFE_MSVC}>:/wd4996>)
set(lint-no-no_wrong_code $<$<BOOL:${LC_LINT_NO_WRONG_CODE}>:/wd4819>)
set(lint-no-builtin-macro-redefined $<$<BOOL:${LC_LINT_BUILTIN_REDEFINE}>:-Wno-builtin-macro-redefined>)


# Unfortunately, this one is complicated because MSVC and clang-cl support -Wall
# but using it is like calling -Weverything
string(CONCAT lint-default $<
	$<AND:$<BOOL:${LC_LINT_WALL}>,$<NOT:${is-msvc}>>:-Wall
>)
set(lint-utf8-msvc $<$<BOOL:${LC_LINT_UTF8_MSVC}>:/utf-8>)

list(APPEND compile_flags_both
	${lint-extra}
	${lint-default}
	${lint-w4}
	${lint-no-unused-parameter}
	${lint-no-unused-parameter-msvc}
	${lint-no-conditional-constant-msvc}
	${lint-no-nonstandard-msvc}
	${lint-no-nonstandard-empty-tu-msvc}
	${lint-no-nonstandard-file-scope-msvc}
	${lint-no-nonstandard-nonstatic-dlimport-msvc}
	${lint-no-hides-local-msvc}
	${lint-no-hides-param-msvc}
	${lint-no-hides-global-msvc}
	${lint-no-conditional-assignment-msvc}
	${lint-no-unsafe-msvc}
	${lint-no-no_wrong_code}
	${lint-no-builtin-macro-redefined}

	)

check_c_compiler_flag(-fno-strict-aliasing 	LC_F_STRICT_ALIASING)
list(APPEND compile_flags_both
	$<$<BOOL:${LC_F_STRICT_ALIASING}>:-fno-strict-aliasing>
)


list(APPEND compile_flags_c
	${lint-strict-prototypes}
)

list(APPEND compile_flags_cxx
)

set(GNUC 0)
set(CLANG 0)
set(MSVC 0)
if (("${CMAKE_C_COMPILER_ID}" STREQUAL "Clang") OR
		("${CMAKE_C_COMPILER_ID}" STREQUAL "AppleClang"))
		set(CLANG 1)
endif()
if (("${CMAKE_C_COMPILER_ID}" STREQUAL "GNU") OR (${CLANG}))
		set(GNUC 1)
endif()
if (("${CMAKE_C_COMPILER_ID}" STREQUAL "MSVC") OR (${CLANG}))
		set(MSVC 1)
endif()

if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -ansi -rdynamic")
	set(CMAKE_C_FLAGS 	"${CMAKE_C_FLAGS} -std=gnu99 -g -rdynamic")

elseif(MSVC)

endif()


if (WIN32)
	set(CMAKE_C_FLAGS_RELEASE      "${CMAKE_C_FLAGS_RELEASE} /MT"      CACHE STRING "")
	set(CMAKE_C_FLAGS_DEBUG        "${CMAKE_C_FLAGS_DEBUG} /MTd"       CACHE STRING "")
	set(CMAKE_CXX_FLAGS_RELEASE    "${CMAKE_CXX_FLAGS_RELEASE} /MT"    CACHE STRING "")
	set(CMAKE_CXX_FLAGS_DEBUG      "${CMAKE_CXX_FLAGS_DEBUG} /MTd"     CACHE STRING "")
endif ()


set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O2" )
set(CMAKE_C_FLAGS_RELEASE 	"${CMAKE_C_FLAGS_RELEASE} -O2" )

if (GNUC)
	list(APPEND cute_libraries pthread dl rt)
endif()

if(THIRD_LIB AND MY_LIB)
message("==================LIB_DIR ${THIRD_LIB} ${MY_LIB}=================")
elseif(WIN32)
	set(THIRD_LIB 	"${PROJECT_SOURCE_DIR}/../third_lib")
	set(MY_LIB 		"${PROJECT_SOURCE_DIR}/../libs")
else()
	set(THIRD_LIB 	"$ENV{HOME}/workspace/third_lib")
	set(MY_LIB 		"$ENV{HOME}/workspace/libs")
endif()
#---------------------------find_library -------------------------------

#zlib
find_path(zlib_inc NAMES zlib.h PATHS ${THIRD_LIB}/zlib/include)
if (WIN32)
	find_library(zlib_lib NAMES zlib PATHS ${THIRD_LIB}/zlib/lib)
else()
	find_library(zlib_lib NAMES libz.a PATHS ${THIRD_LIB}/zlib/lib)
endif()
list(APPEND cute_libraries 	${zlib_lib})
list(APPEND cute_includes 	${zlib_inc})



#libuv
find_path(uv_inc NAMES uv.h PATHS ${THIRD_LIB}/libuv/include)
if (WIN32)
	find_library(uv_lib NAMES uv PATHS ${THIRD_LIB}/libuv/lib)
else()
	find_library(uv_lib NAMES libuv.a PATHS ${THIRD_LIB}/libuv/lib)
endif()
list(APPEND cute_libraries 	${uv_lib})
list(APPEND cute_includes 	${uv_inc})
message("uv find : ${uv_lib}")

#zlog
set(zlog_inc ${THIRD_LIB}/zlog/include)
if (WIN32)
	find_library(zlog_lib NAMES zlog PATHS ${THIRD_LIB}/zlog/lib)
else()
	find_library(zlog_lib NAMES libzlog.a PATHS ${THIRD_LIB}/zlog/lib)
endif()
list(APPEND cute_libraries 	${zlog_lib})
list(APPEND cute_includes 	${zlog_inc})
message("zlog lib: ${zlog_lib}  inc:${zlog_inc}")

# tcmalloc
find_path(tcmalloc_inc NAMES tcmalloc.h	PATHS ${THIRD_LIB}/gperftools/include/gperftools)
if (WIN32)
	find_library(tcmalloc_lib NAMES tcmalloc_minimal PATHS ${THIRD_LIB}/gperftools/lib)
	list(APPEND cute_libraries ${tcmalloc_lib})
else()
	find_library(tcmalloc_lib libtcmalloc.so PATHS ${THIRD_LIB}/gperftools/lib)
	find_library(profiler_lib libprofiler.so PATHS ${THIRD_LIB}/gperftools/lib)
	list(APPEND cute_libraries ${tcmalloc_lib} ${profiler_lib})
endif()
list(APPEND cute_includes 	${tcmalloc_inc})
message("tcmalloc find : ${tcmalloc_lib} inc: ${profiler_lib}" )



#mysql
if(WIN32)
	find_path(mysql_inc 	NAMES mysql.h 		PATHS  ${THIRD_LIB}/mysql/include)
	find_library(mysql_lib 	NAMES libmysql.lib 	PATHS  ${THIRD_LIB}/mysql/lib)
else()
	find_path(mysql_inc NAMES mysql.h PATHS /usr/include/mysql)
	set(mysql_lib "mysqlclient")
endif()

list(APPEND cute_includes ${mysql_inc})
list(APPEND cute_libraries ${mysql_lib})
message("mysql find : ${mysql_inc} inc: ${mysql_lib}" )




#mbedtls
if (WIN32)
	list(APPEND cute_includes 	${THIRD_LIB}/mbedtls/include)
	list(APPEND cute_libraries 	${THIRD_LIB}/mbedtls/lib/mbedtls.lib)
	list(APPEND cute_libraries 	${THIRD_LIB}/mbedtls/lib/mbedcrypto.lib)
	list(APPEND cute_libraries 	${THIRD_LIB}/mbedtls/lib/mbedx509.lib)

else()
	find_package(MbedTLS)
	list(APPEND cute_includes 	/usr/local/include/mbedtls)
	list(APPEND cute_libraries 	/usr/local/lib/libmbedtls.a)
	list(APPEND cute_libraries 	/usr/local/lib/libmbedcrypto.a)
	list(APPEND cute_libraries 	/usr/local/lib/libmbedx509.a)

endif()

#libcopp
set (libcopp_inc ${THIRD_LIB}/libcopp/include)
if(WIN32)
	find_library(libcopp_lib 	NAMES copp.lib 		PATHS  ${THIRD_LIB}/libcopp/lib)
	find_library(libcopp_task 	NAMES cotask.lib 	PATHS  ${THIRD_LIB}/libcopp/lib)
else()
	find_library(libcopp_lib 	NAMES libcopp.a 	PATHS  ${THIRD_LIB}/libcopp/lib)
	find_library(libcopp_task 	NAMES libcotask.a 	PATHS  ${THIRD_LIB}/libcopp/lib)
endif()
list(APPEND cute_includes  ${libcopp_inc})
list(APPEND cute_libraries ${libcopp_lib} ${libcopp_task})

message("libcopp_inc find : ${libcopp_lib}, ${libcopp_task}" )


if(BUILD_DEBUG)
	set(cute 	cuted )
	set(sqlpp 	sqlppd)
else()
	set(cute 	cute)
	set(sqlpp 	sqlpp)
endif()

#sqlpp
if(WIN32)
	find_path(sqlpp_inc 	NAMES sqlpp.h 			PATHS  ${MY_LIB}/libsqlpp/include)
	find_library(sqlpp_lib 	NAMES ${sqlpp}.lib 		PATHS  ${MY_LIB}/libsqlpp/lib)
else()
	find_path(sqlpp_inc 	NAMES sqlpp.h 			PATHS  ${MY_LIB}/libsqlpp/include)
	find_library(sqlpp_lib 	NAMES lib${sqlpp}.a 	PATHS  ${MY_LIB}/libsqlpp/lib)
endif()

list(APPEND cute_includes ${sqlpp_inc})
list(APPEND cute_libraries ${sqlpp_lib})
message("sqlpp find : ${sqlpp_inc} inc: ${sqlpp_lib}" )


set_property(GLOBAL PROPERTY module_inc_list )
set_property(GLOBAL PROPERTY module_lib_list)
function (add_module module )

	if(BUILD_DEBUG)
		set(m_name ${module}d)
	else()
		set(m_name ${module})
	endif()

	if(WIN32)
		find_path 	(${module}_inc 	NAMES lib${module}.h 	PATHS  ${MY_LIB}/lib${module}/include)
		find_library(${module}_lib	NAMES ${m_name}.lib 	PATHS  ${MY_LIB}/lib${module}/lib)
	else()
		find_path 	(${module}_inc 	NAMES lib${module}.h 	PATHS  ${MY_LIB}/lib${module}/include)
		find_library(${module}_lib 	NAMES lib${m_name}.a 	PATHS  ${MY_LIB}/lib${module}/lib)
	endif()
	get_property(inc_list GLOBAL PROPERTY module_inc_list)
	list(APPEND inc_list ${${module}_inc})
	set_property(GLOBAL PROPERTY module_inc_list ${inc_list})

	get_property(lib_list GLOBAL PROPERTY module_lib_list)
	list(APPEND lib_list ${${module}_lib})
	set_property(GLOBAL PROPERTY module_lib_list ${lib_list})

	message(STATUS "add ${module} ${${module}_inc} ${${module}_lib}")
endfunction(add_module)

add_module(cute)
add_module(m_time)
add_module(m_sdvo)
add_module(m_spvo)
add_module(m_placeroute)


get_property(module_inc GLOBAL PROPERTY module_inc_list)
list(APPEND cute_includes 	${module_inc})
get_property(module_lib GLOBAL PROPERTY module_lib_list)
list(APPEND cute_libraries 	${module_lib})

message("cute find : ${cute_inc} inc: ${cute_lib}" )


list(APPEND directories
	src
	src/wrapper
	src/wrapper/dvo
)

include(tools/cmake_tools.cmake)

include_directories(${directories})
foreach(dir IN LISTS directories)
	message("add dir ${dir}")
	aux_source_directory(${dir}  cute_source)
endforeach()

message("cute_source : ${cute_source}")

function (build_lib target)
	add_library(${target} STATIC ${cute_source})
	redefine_file_path(${target})
	target_include_directories(	${target}
								PUBLIC
								${cute_includes}
								)
	target_link_libraries(${target}  ${cute_libraries})
	target_compile_options(${target}
							PRIVATE
							${compile_flags_both}
							$<$<COMPILE_LANGUAGE:C 	 >:${compile_flags_c}>
							$<$<COMPILE_LANGUAGE:CXX >:${compile_flags_cxx}>
							)

	target_precompile_headers(${target}
		PRIVATE
		"pch.h"
	)
endfunction()


build_lib(pcute_aux)

list (APPEND exe_list
	background
)

macro(glob_includes result curdir)
	list(APPEND ${result} ${curdir})
    file(GLOB children ${curdir}/*)
    foreach(child ${children})
        if(IS_DIRECTORY ${child})
        	glob_includes(${result} ${child})
        endif()
    endforeach()
endmacro()


foreach(app IN LISTS exe_list)
	set (app_source) # clear the app_source
	file(GLOB_RECURSE app_source "src_${app}/*.cpp")

	set (app_includes)
	glob_includes(app_includes "src_${app}")

	set (app_module 	"pcute_${app}")

	add_executable(${app_module} ${app_source})
	redefine_file_path(${app_module})
	target_link_libraries(${app_module}
		-Wl,--start-group
		${cute_libraries}
		pcute_aux
		-Wl,--end-group
	)
	target_include_directories(	${app_module}
								PUBLIC
								${app_includes}
								)

	target_compile_options(${app_module}
							PRIVATE
							${compile_flags_both}
							$<$<COMPILE_LANGUAGE:C  >:${compile_flags_c}>
							$<$<COMPILE_LANGUAGE:CXX>:${compile_flags_cxx}>
							)
	target_compile_definitions(
		${app_module}
		PRIVATE
		MODULE_NAME="${app}"
	)
endforeach()



if (MAKE_INSTALL)
	foreach(app IN LISTS exe_list)
		set (app_module 	"pcute_${app}")
		install(TARGETS		${app_module}
				RUNTIME DESTINATION "${CMAKE_INSTALL_BINDIR}"
		)
	endforeach()
endif()



