^D:\BAIDUNETDISKDOWNLOAD\MHGD\RC\PROJECT\USRZONE\SOURCE\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source -BD:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64 --check-stamp-file D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
