^D:\BAIDUNETDISKDOWNLOAD\MHGD\RC\PROJECT\USRZONE\SOURCE\BUILD_WIN64\CMAKEFILES\A46819BA3F9625CAAE4DC677465396CB\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source -BD:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/BaiduNetdiskDownload/mhgd/rc/project/usrzone/source/build_win64/project_usrzone.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
