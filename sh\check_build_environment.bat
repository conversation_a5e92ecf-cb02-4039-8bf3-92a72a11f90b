@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo ========================================
echo Build Environment Diagnostic
echo ========================================

echo.
echo 1. Checking CMake installation...
cmake --version
if errorlevel 1 (
    echo ERROR: CMake not found! Please install CMake.
    set cmake_ok=0
) else (
    echo SUCCESS: CMake is installed
    set cmake_ok=1
)

echo.
echo 2. Checking available CMake generators...
cmake --help | findstr "Visual Studio"

echo.
echo 3. Checking Visual Studio 2022...
cmake --help | findstr "Visual Studio 17 2022" >nul
if errorlevel 1 (
    echo WARNING: Visual Studio 17 2022 not found
    set vs2022_ok=0
) else (
    echo SUCCESS: Visual Studio 17 2022 available
    set vs2022_ok=1
)

echo.
echo 4. Checking Visual Studio 2019...
cmake --help | findstr "Visual Studio 16 2019" >nul
if errorlevel 1 (
    echo WARNING: Visual Studio 16 2019 not found
    set vs2019_ok=0
) else (
    echo SUCCESS: Visual Studio 16 2019 available
    set vs2019_ok=1
)

echo.
echo 5. Checking MSBuild...
where msbuild >nul 2>&1
if errorlevel 1 (
    echo WARNING: MSBuild not found in PATH
    set msbuild_ok=0
) else (
    echo SUCCESS: MSBuild found in PATH
    msbuild -version
    set msbuild_ok=1
)

echo.
echo 6. Checking project structure...
if exist "..\project\usrzone\source\CMakeLists.txt" (
    echo SUCCESS: usrzone project found
) else (
    echo ERROR: usrzone project not found
)

if exist "..\project\usrzone\source\make_win64.bat" (
    echo SUCCESS: usrzone build script found
) else (
    echo ERROR: usrzone build script not found
)

echo.
echo 7. Checking third-party libraries...
if exist "..\project\usrzone\third_lib" (
    echo SUCCESS: third_lib directory found
) else (
    echo WARNING: third_lib directory not found
)

if exist "..\project\usrzone\libs" (
    echo SUCCESS: libs directory found
) else (
    echo WARNING: libs directory not found
)

echo.
echo ========================================
echo DIAGNOSTIC SUMMARY
echo ========================================

if %cmake_ok%==1 (
    echo ✓ CMake: OK
) else (
    echo ✗ CMake: MISSING
)

if %vs2022_ok%==1 (
    echo ✓ Visual Studio 2022: OK
) else if %vs2019_ok%==1 (
    echo ✓ Visual Studio 2019: OK
) else (
    echo ✗ Visual Studio: MISSING
)

if %msbuild_ok%==1 (
    echo ✓ MSBuild: OK
) else (
    echo ⚠ MSBuild: NOT IN PATH
)

echo.
echo RECOMMENDATIONS:
if %cmake_ok%==0 (
    echo - Install CMake from https://cmake.org/download/
)

if %vs2022_ok%==0 if %vs2019_ok%==0 (
    echo - Install Visual Studio 2019 or 2022 with C++ development tools
    echo - Make sure to include CMake tools and Windows SDK
)

if %msbuild_ok%==0 (
    echo - Add MSBuild to your PATH or run from Developer Command Prompt
)

echo.
echo ========================================
pause
