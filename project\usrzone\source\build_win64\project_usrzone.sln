﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{3A76CDFB-8C6A-3FED-8A56-C21C26A45ACF}"
	ProjectSection(ProjectDependencies) = postProject
		{2EE08440-C90A-3B61-B0E4-92FBEEED62CD} = {2EE08440-C90A-3B61-B0E4-92FBEEED62CD}
		{2D6759CD-3415-3438-8872-BA58BD74D4AF} = {2D6759CD-3415-3438-8872-BA58BD74D4AF}
		{28C7A959-F7CB-31EB-87C3-EB074755F77A} = {28C7A959-F7CB-31EB-87C3-EB074755F77A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{339CB16E-5C4E-3C13-ABA6-3CB44F6DFF6B}"
	ProjectSection(ProjectDependencies) = postProject
		{3A76CDFB-8C6A-3FED-8A56-C21C26A45ACF} = {3A76CDFB-8C6A-3FED-8A56-C21C26A45ACF}
		{2EE08440-C90A-3B61-B0E4-92FBEEED62CD} = {2EE08440-C90A-3B61-B0E4-92FBEEED62CD}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{2EE08440-C90A-3B61-B0E4-92FBEEED62CD}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "pcute_aux", "pcute_aux.vcxproj", "{2D6759CD-3415-3438-8872-BA58BD74D4AF}"
	ProjectSection(ProjectDependencies) = postProject
		{2EE08440-C90A-3B61-B0E4-92FBEEED62CD} = {2EE08440-C90A-3B61-B0E4-92FBEEED62CD}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "pcute_usrzone", "pcute_usrzone.vcxproj", "{28C7A959-F7CB-31EB-87C3-EB074755F77A}"
	ProjectSection(ProjectDependencies) = postProject
		{2EE08440-C90A-3B61-B0E4-92FBEEED62CD} = {2EE08440-C90A-3B61-B0E4-92FBEEED62CD}
		{2D6759CD-3415-3438-8872-BA58BD74D4AF} = {2D6759CD-3415-3438-8872-BA58BD74D4AF}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{3A76CDFB-8C6A-3FED-8A56-C21C26A45ACF}.Debug|x64.ActiveCfg = Debug|x64
		{3A76CDFB-8C6A-3FED-8A56-C21C26A45ACF}.Debug|x64.Build.0 = Debug|x64
		{3A76CDFB-8C6A-3FED-8A56-C21C26A45ACF}.Release|x64.ActiveCfg = Release|x64
		{3A76CDFB-8C6A-3FED-8A56-C21C26A45ACF}.Release|x64.Build.0 = Release|x64
		{3A76CDFB-8C6A-3FED-8A56-C21C26A45ACF}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{3A76CDFB-8C6A-3FED-8A56-C21C26A45ACF}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{3A76CDFB-8C6A-3FED-8A56-C21C26A45ACF}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{3A76CDFB-8C6A-3FED-8A56-C21C26A45ACF}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{339CB16E-5C4E-3C13-ABA6-3CB44F6DFF6B}.Debug|x64.ActiveCfg = Debug|x64
		{339CB16E-5C4E-3C13-ABA6-3CB44F6DFF6B}.Release|x64.ActiveCfg = Release|x64
		{339CB16E-5C4E-3C13-ABA6-3CB44F6DFF6B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{339CB16E-5C4E-3C13-ABA6-3CB44F6DFF6B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{2EE08440-C90A-3B61-B0E4-92FBEEED62CD}.Debug|x64.ActiveCfg = Debug|x64
		{2EE08440-C90A-3B61-B0E4-92FBEEED62CD}.Debug|x64.Build.0 = Debug|x64
		{2EE08440-C90A-3B61-B0E4-92FBEEED62CD}.Release|x64.ActiveCfg = Release|x64
		{2EE08440-C90A-3B61-B0E4-92FBEEED62CD}.Release|x64.Build.0 = Release|x64
		{2EE08440-C90A-3B61-B0E4-92FBEEED62CD}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{2EE08440-C90A-3B61-B0E4-92FBEEED62CD}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{2EE08440-C90A-3B61-B0E4-92FBEEED62CD}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{2EE08440-C90A-3B61-B0E4-92FBEEED62CD}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{2D6759CD-3415-3438-8872-BA58BD74D4AF}.Debug|x64.ActiveCfg = Debug|x64
		{2D6759CD-3415-3438-8872-BA58BD74D4AF}.Debug|x64.Build.0 = Debug|x64
		{2D6759CD-3415-3438-8872-BA58BD74D4AF}.Release|x64.ActiveCfg = Release|x64
		{2D6759CD-3415-3438-8872-BA58BD74D4AF}.Release|x64.Build.0 = Release|x64
		{2D6759CD-3415-3438-8872-BA58BD74D4AF}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{2D6759CD-3415-3438-8872-BA58BD74D4AF}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{2D6759CD-3415-3438-8872-BA58BD74D4AF}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{2D6759CD-3415-3438-8872-BA58BD74D4AF}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{28C7A959-F7CB-31EB-87C3-EB074755F77A}.Debug|x64.ActiveCfg = Debug|x64
		{28C7A959-F7CB-31EB-87C3-EB074755F77A}.Debug|x64.Build.0 = Debug|x64
		{28C7A959-F7CB-31EB-87C3-EB074755F77A}.Release|x64.ActiveCfg = Release|x64
		{28C7A959-F7CB-31EB-87C3-EB074755F77A}.Release|x64.Build.0 = Release|x64
		{28C7A959-F7CB-31EB-87C3-EB074755F77A}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{28C7A959-F7CB-31EB-87C3-EB074755F77A}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{28C7A959-F7CB-31EB-87C3-EB074755F77A}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{28C7A959-F7CB-31EB-87C3-EB074755F77A}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A6164D3E-9D30-3F75-AC39-66391F49ACD9}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
