﻿@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

if "%1"=="" (
    echo �÷�: make_single.bat [��Ŀ����] [��ѡ����...]
    echo.
    echo ���õ���Ŀ����:
    echo   usrzone, usrcenter, route, gatetcp, gatehttp
    echo   gameserver, central, placeroute, daemon, chat
    echo   background, log, sdk, errorlog, rdzone
    echo   team, friend, confighttp
    echo.
    echo ʾ��: make_single.bat gameserver
    echo       make_single.bat usrzone clean
    exit /b 1
)

set project_name=%1
shift
set args=%*

echo ========================================
echo ����������Ŀ: %project_name%
echo ========================================

if exist "make_%project_name%.bat" (
    call make_%project_name%.bat %args%
    if errorlevel 1 (
        echo %project_name% ����ʧ�ܣ�
        exit /b 1
    ) else (
        echo %project_name% �����ɹ���
    )
) else (
    echo ����: �Ҳ�����Ŀ %project_name% �Ĺ����ű���
    echo ������Ŀ�����Ƿ���ȷ��
    exit /b 1
)

echo ========================================

