﻿#cmake_tools.cmake

cmake_minimum_required (VERSION 3.10)

project ("project_mhgd")


list(APPEND CMAKE_MODULE_PATH "${PROJECT_SOURCE_DIR}/cmake")

set(BUILD_DEBUG 	0)
set(BUILD_RELEASE 	0)

if(${CMAKE_BUILD_TYPE} STREQUAL "Debug")
	set(BUILD_DEBUG 1)
else()
	set(BUILD_RELEASE 1)
endif()

option(MAKE_INSTALL 	"run install process" 	OFF)
option(BUILD_TESTING 	"run testing" 			OFF)
include(CTest)

include(CMakePackageConfigHelpers)
include(CMakeDependentOption)
include(CheckCCompilerFlag)
include(GNUInstallDirs)

set(CMAKE_C_VISIBILITY_PRESET hidden)
set(CMAKE_C_STANDARD_REQUIRED ON)
set(CMAKE_C_EXTENSIONS ON)
set(CMAKE_C_STANDARD 99)

set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_STANDARD 17)

# Compiler check
string(CONCAT is-msvc $<OR:
	$<C_COMPILER_ID:MSVC>,
	$<STREQUAL:${CMAKE_C_COMPILER_FRONTEND_VARIANT},MSVC>
>)

# check_c_compiler_flag(/W4 		LC_LINT_W4)
check_c_compiler_flag(/wd4100 	LC_LINT_NO_UNUSED_PARAMETER_MSVC)
check_c_compiler_flag(/wd4127 	LC_LINT_NO_CONDITIONAL_CONSTANT_MSVC)
check_c_compiler_flag(/wd4201 	LC_LINT_NO_NONSTANDARD_MSVC)
check_c_compiler_flag(/wd4206 	LC_LINT_NO_NONSTANDARD_EMPTY_TU_MSVC)
check_c_compiler_flag(/wd4210 	LC_LINT_NO_NONSTANDARD_FILE_SCOPE_MSVC)
check_c_compiler_flag(/wd4232 	LC_LINT_NO_NONSTANDARD_NONSTATIC_DLIMPORT_MSVC)
check_c_compiler_flag(/wd4456 	LC_LINT_NO_HIDES_LOCAL)
check_c_compiler_flag(/wd4457 	LC_LINT_NO_HIDES_PARAM)
check_c_compiler_flag(/wd4459 	LC_LINT_NO_HIDES_GLOBAL)
check_c_compiler_flag(/wd4706 	LC_LINT_NO_CONDITIONAL_ASSIGNMENT_MSVC)
check_c_compiler_flag(/wd4996 	LC_LINT_NO_UNSAFE_MSVC)
check_c_compiler_flag(/wd4819 	LC_LINT_NO_WRONG_CODE)

check_c_compiler_flag(-Wall 	LC_LINT_WALL) # DO NOT use this under MSVC

check_c_compiler_flag(-Wno-unused-parameter LC_LINT_NO_UNUSED_PARAMETER)
check_c_compiler_flag(-Wstrict-prototypes 	LC_LINT_STRICT_PROTOTYPES)
check_c_compiler_flag(-Wextra 				LC_LINT_EXTRA)

check_c_compiler_flag(-Wno-builtin-macro-redefined LC_LINT_BUILTIN_REDEFINE)


check_c_compiler_flag(/utf-8 LC_LINT_UTF8_MSVC)

set(lint-no-unused-parameter $<$<BOOL:${LC_LINT_NO_UNUSED_PARAMETER}>:-Wno-unused-parameter>)
set(lint-strict-prototypes $<$<BOOL:${LC_LINT_STRICT_PROTOTYPES}>:-Wstrict-prototypes>)
set(lint-extra $<$<BOOL:${LC_LINT_EXTRA}>:-Wextra>)
set(lint-no-unused-parameter-msvc $<$<BOOL:${LC_LINT_NO_UNUSED_PARAMETER_MSVC}>:/wd4100>)
set(lint-no-conditional-constant-msvc $<$<BOOL:${LC_LINT_NO_CONDITIONAL_CONSTANT_MSVC}>:/wd4127>)
set(lint-no-nonstandard-msvc $<$<BOOL:${LC_LINT_NO_NONSTANDARD_MSVC}>:/wd4201>)
set(lint-no-nonstandard-empty-tu-msvc $<$<BOOL:${LC_LINT_NO_NONSTANDARD_EMPTY_TU_MSVC}>:/wd4206>)
set(lint-no-nonstandard-file-scope-msvc $<$<BOOL:${LC_LINT_NO_NONSTANDARD_FILE_SCOPE_MSVC}>:/wd4210>)
set(lint-no-nonstandard-nonstatic-dlimport-msvc $<$<BOOL:${LC_LINT_NO_NONSTANDARD_NONSTATIC_DLIMPORT_MSVC}>:/wd4232>)
set(lint-no-hides-local-msvc $<$<BOOL:${LC_LINT_NO_HIDES_LOCAL}>:/wd4456>)
set(lint-no-hides-param-msvc $<$<BOOL:${LC_LINT_NO_HIDES_PARAM}>:/wd4457>)
set(lint-no-hides-global-msvc $<$<BOOL:${LC_LINT_NO_HIDES_GLOBAL}>:/wd4459>)
set(lint-no-conditional-assignment-msvc $<$<BOOL:${LC_LINT_NO_CONDITIONAL_ASSIGNMENT_MSVC}>:/wd4706>)
set(lint-no-unsafe-msvc $<$<BOOL:${LC_LINT_NO_UNSAFE_MSVC}>:/wd4996>)
set(lint-no-no_wrong_code $<$<BOOL:${LC_LINT_NO_WRONG_CODE}>:/wd4819>)
set(lint-no-builtin-macro-redefined $<$<BOOL:${LC_LINT_BUILTIN_REDEFINE}>:-Wno-builtin-macro-redefined>)


# Unfortunately, this one is complicated because MSVC and clang-cl support -Wall
# but using it is like calling -Weverything
string(CONCAT lint-default $<
	$<AND:$<BOOL:${LC_LINT_WALL}>,$<NOT:${is-msvc}>>:-Wall
>)
set(lint-utf8-msvc $<$<BOOL:${LC_LINT_UTF8_MSVC}>:/utf-8>)

list(APPEND target_options
	${lint-extra}
	${lint-default}
	${lint-w4}
	${lint-no-unused-parameter}
	${lint-no-unused-parameter-msvc}
	${lint-no-conditional-constant-msvc}
	${lint-no-nonstandard-msvc}
	${lint-no-nonstandard-empty-tu-msvc}
	${lint-no-nonstandard-file-scope-msvc}
	${lint-no-nonstandard-nonstatic-dlimport-msvc}
	${lint-no-hides-local-msvc}
	${lint-no-hides-param-msvc}
	${lint-no-hides-global-msvc}
	${lint-no-conditional-assignment-msvc}
	${lint-no-unsafe-msvc}
	${lint-no-no_wrong_code}
	${lint-no-builtin-macro-redefined}

	)

check_c_compiler_flag(-fno-strict-aliasing 	LC_F_STRICT_ALIASING)
list(APPEND target_options
	$<$<BOOL:${LC_F_STRICT_ALIASING}>:-fno-strict-aliasing>
)

list(APPEND target_options_c
	${lint-strict-prototypes}
)

list(APPEND target_options_cxx
)

set(GNUC 0)
set(CLANG 0)
set(MSVC 0)
if (("${CMAKE_C_COMPILER_ID}" STREQUAL "Clang") OR
		("${CMAKE_C_COMPILER_ID}" STREQUAL "AppleClang"))
		set(CLANG 1)
endif()
if (("${CMAKE_C_COMPILER_ID}" STREQUAL "GNU") OR (${CLANG}))
		set(GNUC 1)
endif()
if (("${CMAKE_C_COMPILER_ID}" STREQUAL "MSVC") OR (${CLANG}))
		set(MSVC 1)
endif()

if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -ansi -rdynamic")
	set(CMAKE_C_FLAGS 	"${CMAKE_C_FLAGS} -std=gnu99 -g -rdynamic")

elseif(MSVC)

endif()


if (WIN32)
	set(CMAKE_C_FLAGS_RELEASE      "${CMAKE_C_FLAGS_RELEASE} /MT"      CACHE STRING "")
	set(CMAKE_C_FLAGS_DEBUG        "${CMAKE_C_FLAGS_DEBUG} /MTd"       CACHE STRING "")
	set(CMAKE_CXX_FLAGS_RELEASE    "${CMAKE_CXX_FLAGS_RELEASE} /MT"    CACHE STRING "")
	set(CMAKE_CXX_FLAGS_DEBUG      "${CMAKE_CXX_FLAGS_DEBUG} /MTd"     CACHE STRING "")
endif ()


set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O2" )
set(CMAKE_C_FLAGS_RELEASE 	"${CMAKE_C_FLAGS_RELEASE} -O2" )

if (GNUC)
	list(APPEND target_libraries pthread dl rt)
endif()

if(THIRD_LIB AND MY_LIB)
message("==================LIB_DIR ${THIRD_LIB} ${MY_LIB}=================")
elseif(WIN32)
	set(THIRD_LIB 	"${PROJECT_SOURCE_DIR}/../third_lib")
	set(MY_LIB 		"${PROJECT_SOURCE_DIR}/../libs")
else()
	set(THIRD_LIB 	"$ENV{HOME}/workspace/third_lib")
	set(MY_LIB 		"$ENV{HOME}/workspace/libs")
endif()
#---------------------------find_library -------------------------------

#zlib
find_path(zlib_inc NAMES zlib.h PATHS ${THIRD_LIB}/zlib/include)
if (WIN32)
	find_library(zlib_lib NAMES zlib PATHS ${THIRD_LIB}/zlib/lib)
else()
	find_library(zlib_lib NAMES libz.a PATHS ${THIRD_LIB}/zlib/lib)
endif()
list(APPEND target_libraries 	${zlib_lib})
list(APPEND target_includes 	${zlib_inc})




#libuv
find_path(uv_inc NAMES uv.h PATHS ${THIRD_LIB}/libuv/include)
if (WIN32)
	find_library(uv_lib NAMES uv PATHS ${THIRD_LIB}/libuv/lib)
else()
	find_library(uv_lib NAMES libuv.a PATHS ${THIRD_LIB}/libuv/lib)
endif()
list(APPEND target_libraries 	${uv_lib})
list(APPEND target_includes 	${uv_inc})
message("uv find : ${uv_lib}")

#zlog
set(zlog_inc ${THIRD_LIB}/zlog/include)
if (WIN32)
	find_library(zlog_lib NAMES zlog PATHS ${THIRD_LIB}/zlog/lib)
else()
	find_library(zlog_lib NAMES libzlog.a PATHS ${THIRD_LIB}/zlog/lib)
endif()
list(APPEND target_libraries 	${zlog_lib})
list(APPEND target_includes 	${zlog_inc})
message("zlog lib: ${zlog_lib}  inc:${zlog_inc}")

# tcmalloc
find_path(tcmalloc_inc NAMES tcmalloc.h	PATHS ${THIRD_LIB}/gperftools/include/gperftools)
if (WIN32)
	find_library(tcmalloc_lib NAMES tcmalloc_minimal PATHS ${THIRD_LIB}/gperftools/lib)
	list(APPEND target_libraries ${tcmalloc_lib})
else()
	find_library(tcmalloc_lib libtcmalloc.so PATHS ${THIRD_LIB}/gperftools/lib)
	find_library(profiler_lib libprofiler.so PATHS ${THIRD_LIB}/gperftools/lib)
	list(APPEND target_libraries ${tcmalloc_lib} ${profiler_lib})
endif()
list(APPEND target_includes 	${tcmalloc_inc})
message("tcmalloc find : ${tcmalloc_lib} inc: ${profiler_lib}" )

#mysql
if(WIN32)
	find_path(mysql_inc 	NAMES mysql.h 		PATHS  ${THIRD_LIB}/mysql/include)
	find_library(mysql_lib 	NAMES libmysql.lib 	PATHS  ${THIRD_LIB}/mysql/lib)
else()
	find_path(mysql_inc NAMES mysql.h PATHS /usr/include/mysql)
	set(mysql_lib "mysqlclient")
endif()

list(APPEND target_includes ${mysql_inc})
list(APPEND target_libraries ${mysql_lib})
message("mysql find : ${mysql_inc} inc: ${mysql_lib}" )

#sqlpp
if(WIN32)
	find_path(sqlpp_inc 	NAMES sqlpp.h 		PATHS  ${MY_LIB}/libsqlpp/include)
	find_library(sqlpp_lib 	NAMES sqlpp.lib 	PATHS  ${MY_LIB}/libsqlpp/lib)
else()
	find_path(sqlpp_inc 	NAMES sqlpp.h 		PATHS  ${MY_LIB}/libsqlpp/include)
	find_library(sqlpp_lib 	NAMES libsqlpp.a 	PATHS  ${MY_LIB}/libsqlpp/lib)
endif()
list(APPEND target_includes ${sqlpp_inc})
list(APPEND target_libraries ${sqlpp_lib})
message("sqlpp find : ${sqlpp_inc} inc: ${sqlpp_lib}" )


#mbedtls
if (WIN32)
	list(APPEND target_includes 	${THIRD_LIB}/mbedtls/include)
	list(APPEND target_libraries 	${THIRD_LIB}/mbedtls/lib/mbedtls.lib)
	list(APPEND target_libraries 	${THIRD_LIB}/mbedtls/lib/mbedcrypto.lib)
	list(APPEND target_libraries 	${THIRD_LIB}/mbedtls/lib/mbedx509.lib)

else()
	find_package(MbedTLS)
	list(APPEND target_includes 	/usr/local/include/mbedtls)
	list(APPEND target_libraries 	/usr/local/lib/libmbedtls.a)
	list(APPEND target_libraries 	/usr/local/lib/libmbedcrypto.a)
	list(APPEND target_libraries 	/usr/local/lib/libmbedx509.a)

endif()

#libcopp
set (libcopp_inc ${THIRD_LIB}/libcopp/include)
if(WIN32)
	find_library(libcopp_lib 	NAMES copp.lib 		PATHS  ${THIRD_LIB}/libcopp/lib)
	find_library(libcopp_task 	NAMES cotask.lib 	PATHS  ${THIRD_LIB}/libcopp/lib)
else()
	find_library(libcopp_lib 	NAMES libcopp.a 	PATHS  ${THIRD_LIB}/libcopp/lib)
	find_library(libcopp_task 	NAMES libcotask.a 	PATHS  ${THIRD_LIB}/libcopp/lib)
endif()
list(APPEND target_includes  ${libcopp_inc})
list(APPEND target_libraries ${libcopp_lib} ${libcopp_task})

message("libcopp_inc find : ${libcopp_lib}, ${libcopp_task}" )


set_property(GLOBAL PROPERTY module_inc_list )
set_property(GLOBAL PROPERTY module_lib_list)
function (add_module module    )

	if(BUILD_DEBUG)
		set(m_name ${module}d)
	else()
		set(m_name ${module})
	endif()

	if(WIN32)
		find_path 	(${module}_inc 	NAMES lib${module}.h 	PATHS  ${MY_LIB}/lib${module}/include)
		find_library(${module}_lib	NAMES ${m_name}.lib 	PATHS  ${MY_LIB}/lib${module}/lib)
	else()
		find_path 	(${module}_inc 	NAMES lib${module}.h 	PATHS  ${MY_LIB}/lib${module}/include)
		find_library(${module}_lib 	NAMES lib${m_name}.a 	PATHS  ${MY_LIB}/lib${module}/lib)
	endif()
	get_property(inc_list GLOBAL PROPERTY module_inc_list)
	list(APPEND inc_list ${${module}_inc})
	set_property(GLOBAL PROPERTY module_inc_list ${inc_list})

	get_property(lib_list GLOBAL PROPERTY module_lib_list)
	list(APPEND lib_list ${${module}_lib})
	set_property(GLOBAL PROPERTY module_lib_list ${lib_list})

	message(STATUS "add ${module} ${${module}_inc} ${${module}_lib}")
endfunction(add_module)

add_module(cute)
add_module(cpx)
add_module(nav)
add_module(game)
add_module(m_time)
add_module(m_props)
add_module(m_buff)
add_module(m_mission)
add_module(m_period)
add_module(m_loot)
add_module(m_scene_ss)
add_module(m_placeroute)
add_module(m_spvo)

get_property(module_inc GLOBAL PROPERTY module_inc_list)
list(APPEND target_includes 	${module_inc})
get_property(module_lib GLOBAL PROPERTY module_lib_list)
list(APPEND target_libraries 	${module_lib})

list(APPEND directories
	src
	src/config
	src/enums
	src/ai
	src/service
	src/rdzone
	src/biz
	src/biz/tvo
	src/biz/tvo/team
	src/biz/tvo/brave
	src/biz/tvo/party
	src/biz/tvo/rank
	src/biz/tvo/market
	src/biz/tvo/guild
	src/biz/tvo/worldlevel
	src/biz/tvo/bet
	src/biz/tvo/activity
	src/biz/tvo/monsterbook
	src/biz/tvo/props
	src/biz/tvo/bodypos
	src/biz/tvo/commerce
	src/biz/tvo/feathergod
	src/biz/tvo/skycity
	src/biz/tvo/rune
	src/biz/tvo/mall
	src/biz/tvo/demofield
	src/biz/tvo/digtreasure
	src/biz/tvo/dragonball
	src/biz/tvo/drawcard
	src/biz/tvo/entrustmission
	src/biz/tvo/vipcard
	src/biz/tvo/offlinereward
	src/biz/tvo/autobuy
	src/biz/tvo/minesweeper
	src/biz/tvo/knowledgemarathon
	src/biz/tvo/friendship
	src/biz/tvo/league
	src/biz/tvo/purify
	src/biz/tvo/arena
	src/biz/tvo/altar
	src/biz/tvo/trade
	src/biz/tvo/limitgift
	src/biz/tvo/mission
	src/biz/tvo/welfareboss
	src/biz/tvo/roundup

	src/biz/etree/ad
	# src/biz/etree/assistant
	src/biz/etree/props
	src/biz/etree/props_extradata
	src/biz/etree/syschat
	src/biz/etree/syschat/deco
	src/biz/etree/syschat/deathraid
	src/biz/etree/syschat/league
	src/biz/etree/vip
	src/biz/etree/scene_act

	src/biz/tvox

	src/biz/dvo
	src/biz/dvo/bet
	src/biz/dvo/team
	src/biz/dvo/brave
	src/biz/dvo/chat
	src/biz/dvo/eliteboss
	src/biz/dvo/friend
	src/biz/dvo/party
	src/biz/dvo/rank
	src/biz/dvo/market
	src/biz/dvo/guild
	src/biz/dvo/trade

	src/biz/dvo/dragonball
	src/biz/dvo/activity
	src/biz/dvo/commerce
	src/biz/dvo/skycity
	src/biz/dvo/drawcard
	src/biz/dvo/jobtitle
	src/biz/dvo/entrustmission
	src/biz/dvo/vipcard
	src/biz/dvo/offlinereward
	src/biz/dvo/digtreasure
	src/biz/dvo/minesweeper
	src/biz/dvo/knowledgemarathon
	src/biz/dvo/league
	src/biz/dvo/prop
	src/biz/dvo/arena
	src/biz/dvo/altar
	src/biz/dvo/rune
	src/biz/dvo/limitgift
	src/biz/dvo/mission
	src/biz/dvo/welfareboss
	src/biz/dvo/roundup

	src/biz/mirror

	src/biz/thing

	src/biz/mission/action
	src/biz/mission/message
	src/biz/mission/vo
	src/biz/mission/deco
	src/biz/mission/tutor

	src/biz/servcmd
	src/biz/servcmd/customvo
	src/biz/servcmd/changeline
	src/biz/servcmd/chat


	src/biz/servcmd/achievement
	src/biz/servcmd/skill
	src/biz/servcmd/team
	src/biz/servcmd/prop
	src/biz/servcmd/bag
	src/biz/servcmd/charge
	src/biz/servcmd/servtools
	src/biz/servcmd/accumulator
	src/biz/servcmd/bet
	src/biz/servcmd/debug
	src/biz/servcmd/bodyposition
	src/biz/servcmd/gamesetting
	src/biz/servcmd/mustdailymission
	src/biz/servcmd/dailycheckin
	src/biz/servcmd/dailyopt
	src/biz/servcmd/skycity
	src/biz/servcmd/jobtitle
	src/biz/servcmd/mail
	src/biz/servcmd/mall
	src/biz/servcmd/merchandize
	src/biz/servcmd/mount
	src/biz/servcmd/onlineaward
	src/biz/servcmd/player
	src/biz/servcmd/rank
	src/biz/servcmd/newcomeraward
	src/biz/servcmd/rune
	src/biz/servcmd/equipmentcollection
	src/biz/servcmd/fashion
	src/biz/servcmd/mission
	src/biz/servcmd/field
	src/biz/servcmd/field/demonfield
	src/biz/servcmd/field/purify
	src/biz/servcmd/featurepreview
	src/biz/servcmd/activity
	src/biz/servcmd/feathergod
	src/biz/servcmd/monsterbook
	src/biz/servcmd/commerce
	src/biz/servcmd/gem
	src/biz/servcmd/dragonball
	src/biz/servcmd/drawcard
	src/biz/servcmd/entrustmission
	src/biz/servcmd/vipcard
	src/biz/servcmd/digtreasure
	src/biz/servcmd/minesweeper
	src/biz/servcmd/guild
	src/biz/servcmd/pksingle
	src/biz/servcmd/arena
	src/biz/servcmd/field/altar
	src/biz/servcmd/limitgift
	src/biz/servcmd/escort
	src/biz/servcmd/welfareboss
	src/biz/servcmd/roundup

	src/biz/rdzone
	src/biz/rdzone/commerce
	src/biz/worker
	src/biz/worker/bag
	src/biz/worker/pet
	src/biz/worker/team
	src/biz/worker/brave
	src/biz/worker/flushnpc
	src/biz/worker/worldlevel
	src/biz/worker/gamesetting
	src/biz/worker/friend
	src/biz/worker/party
	src/biz/worker/rank
	src/biz/worker/rank/handle
	src/biz/worker/market
	src/biz/worker/playerscene
	src/biz/worker/query
	src/biz/worker/field
	src/biz/worker/field/altar
	src/biz/worker/mission
	src/biz/worker/feathergod
	src/biz/worker/achievement
	src/biz/worker/skycity
	src/biz/worker/drop
	src/biz/worker/potion
	src/biz/worker/onlineaward
	src/biz/worker/wartreasure
	src/biz/worker/monsterbook
	src/biz/worker/uzone
	src/biz/worker/commerce
	src/biz/worker/autorecycle
	src/biz/worker/mzone
	src/biz/worker/syschat
	src/biz/worker/bodyposition
	src/biz/worker/digtreasure
	src/biz/worker/dragonball
	src/biz/worker/activity
	src/biz/worker/drawcard
	src/biz/worker/mail
	src/biz/worker/login
	src/biz/worker/jobtitle
	src/biz/worker/props
	src/biz/worker/entrustmission
	src/biz/worker/charge
	src/biz/worker/vipcard
	src/biz/worker/offlinereward
	src/biz/worker/pksingle
	src/biz/worker/league
	src/biz/worker/property
	src/biz/worker/arena
	src/biz/worker/rune
	src/biz/worker/limitgift
	src/biz/worker/roundup

	src/biz/npcdefine

	src/biz/create
	src/comp
	src/pb

	src/module/m_guild
	src/module/m_guild/scene
	src/module/m_guild/servcmd
	src/module/m_guild/sync

	src/module/m_rank/
	src/module/m_rank/servcmd
	src/module/m_rank/sync
	src/module/m_rank/notifier

	src/module/m_chat

	src/module/m_trade/
	src/module/m_trade/servcmd
	src/module/m_trade/sync

	src/module/m_escort/
	src/module/m_escort/servcmd
	src/module/m_escort/sync
	src/module/m_escort/relation
	src/module/m_welfareboss

	src/module/m_merge_server/
	src/module/m_merge_server/sync


 	src/fight
 	src/buff

	src/impl

	src/pipe



	src/cpx/machine
	src/cpx/machine/action
	src/cpx/machine/condition
	src/cpx/skill

	src/entity/auxiliary
	src/entity/robot
	src/entity/npc

	src/scene
	src/scene/callback
	src/scene/entity
	src/scene/sworker
	src/scene/transfer
	src/mono/npc
	src/mono/guild
	src/mono/escort
	src/mono/welfareboss
	src/mono/round_up

	src/wrapper/tvo
	src/wrapper/dvo
	src/wrapper/cpx
	src/wrapper/etree
	src/wrapper/enums
	src/wrapper/mission
	src/wrapper/mono
	src/wrapper/server
	src/wrapper/tree

	src/scene/ai/cpx/action
)

include(tools/cmake_tools.cmake)

include_directories(${directories})
foreach(dir IN LISTS directories)
	# message("add dir ${dir}")
	aux_source_directory(${dir}  target_source)
endforeach()

function (build_lib target)
	add_library(${target} STATIC ${target_source})
	redefine_file_path(${target})
	target_include_directories(	${target}
								PUBLIC
								${target_includes}
								)
	target_link_libraries(${target}  ${target_libraries})
	target_compile_options(${target}
							PRIVATE
							${target_options}
							$<$<COMPILE_LANGUAGE:C 	 >:${target_options_c}>
							$<$<COMPILE_LANGUAGE:CXX >:${target_options_cxx}>
							)
	target_precompile_headers(${target}
		PRIVATE
		"pch.h"
	)

endfunction()


build_lib(pcute_aux)

list (APPEND exe_list
	mhgd
)

macro(glob_includes result curdir)
	list(APPEND ${result} ${curdir})
    file(GLOB children ${curdir}/*)
    foreach(child ${children})
        if(IS_DIRECTORY ${child})
        	glob_includes(${result} ${child})
        endif()
    endforeach()
endmacro()


foreach(app IN LISTS exe_list)
	set (app_source) # clear the app_source
	file(GLOB_RECURSE app_source "src_${app}/*.cpp")

	set (app_includes)
	glob_includes(app_includes "src_${app}")

	set (app_module 	"pcute_${app}")

	add_executable(${app_module} ${app_source})
	redefine_file_path(${app_module})
	target_link_libraries(${app_module}
		-Wl,--start-group
		${target_libraries}
		pcute_aux
		-Wl,--end-group
	)
	target_include_directories(	${app_module}
								PUBLIC
								${app_includes}
								)

	target_compile_options(${app_module}
							PRIVATE
							${target_options}
							$<$<COMPILE_LANGUAGE:C  >:${target_options_c}>
							$<$<COMPILE_LANGUAGE:CXX>:${target_options_cxx}>
							)
	target_compile_definitions(
		${app_module}
		PRIVATE
		MODULE_NAME="${app}"
	)
	message("add app: ${app_module}")

	target_precompile_headers(${app_module}
		PRIVATE
		"pch.h"
	)

endforeach()


if (MAKE_INSTALL)
	foreach(app IN LISTS exe_list)
		set (app_module 	"pcute_${app}")
		install(TARGETS		${app_module}
				RUNTIME DESTINATION "${CMAKE_INSTALL_BINDIR}"
		)
	endforeach()
endif()
