@echo off
echo ========================================
echo Build Environment Check
echo ========================================

echo.
echo Checking CMake...
cmake --version

echo.
echo Checking Visual Studio generators...
cmake --help | findstr "Visual Studio"

echo.
echo Checking project files...
if exist "project\usrzone\source\CMakeLists.txt" (
    echo SUCCESS: usrzone CMakeLists.txt found
) else (
    echo ERROR: usrzone CMakeLists.txt not found
)

if exist "project\usrzone\source\make_win64.bat" (
    echo SUCCESS: usrzone make_win64.bat found
) else (
    echo ERROR: usrzone make_win64.bat not found
)

echo.
echo ========================================
pause
