﻿@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Building all projects (Windows x64)
echo ========================================

set args=%*
set error_count=0

echo [1/18] Building usrzone...
call make_usrzone.bat %args%
if errorlevel 1 set /a error_count+=1

echo [2/18] Building usrcenter...
call make_usrcenter.bat %args%
if errorlevel 1 set /a error_count+=1

echo [3/18] Building route...
call make_route.bat %args%
if errorlevel 1 set /a error_count+=1

echo [4/18] Building gatetcp...
call make_gatetcp.bat %args%
if errorlevel 1 set /a error_count+=1

echo [5/18] Building gatehttp...
call make_gatehttp.bat %args%
if errorlevel 1 set /a error_count+=1

echo [6/18] Building gameserver...
call make_gameserver.bat %args%
if errorlevel 1 set /a error_count+=1

echo [7/18] Building central...
call make_central.bat %args%
if errorlevel 1 set /a error_count+=1

echo [8/18] Building placeroute...
call make_placeroute.bat %args%
if errorlevel 1 set /a error_count+=1

echo [9/18] Building daemon...
call make_daemon.bat %args%
if errorlevel 1 set /a error_count+=1

echo [10/18] Building chat...
call make_chat.bat %args%
if errorlevel 1 set /a error_count+=1

echo [11/18] Building background...
call make_background.bat %args%
if errorlevel 1 set /a error_count+=1

echo [12/18] Building log...
call make_log.bat %args%
if errorlevel 1 set /a error_count+=1

echo [13/18] Building sdk...
call make_sdk.bat %args%
if errorlevel 1 set /a error_count+=1

echo [14/18] Building errorlog...
call make_errorlog.bat %args%
if errorlevel 1 set /a error_count+=1

echo [15/18] Building rdzone...
call make_rdzone.bat %args%
if errorlevel 1 set /a error_count+=1

echo [16/18] Building team...
call make_team.bat %args%
if errorlevel 1 set /a error_count+=1

echo [17/18] Building friend...
call make_friend.bat %args%
if errorlevel 1 set /a error_count+=1

echo [18/18] Building confighttp...
call make_confighttp.bat %args%
if errorlevel 1 set /a error_count+=1

echo ========================================
if %error_count% equ 0 (
    echo BUILD SUCCESS: All projects built successfully!
) else (
    echo BUILD FAILED: %error_count% projects failed to build!
    exit /b 1
)
echo ========================================

