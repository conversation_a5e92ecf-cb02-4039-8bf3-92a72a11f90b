@echo off
echo Cleaning build directories...

if exist "project\usrzone\source\build_win64" (
    echo Removing usrzone build directory...
    rmdir /s /q "project\usrzone\source\build_win64"
)

if exist "project\usrcenter\source\build_win64" (
    echo Removing usrcenter build directory...
    rmdir /s /q "project\usrcenter\source\build_win64"
)

if exist "project\route\source\build_win64" (
    echo Removing route build directory...
    rmdir /s /q "project\route\source\build_win64"
)

if exist "project\gatetcp\source\build_win64" (
    echo Removing gatetcp build directory...
    rmdir /s /q "project\gatetcp\source\build_win64"
)

if exist "project\gatehttp\source\build_win64" (
    echo Removing gatehttp build directory...
    rmdir /s /q "project\gatehttp\source\build_win64"
)

if exist "project\gameserver\source\build_win64" (
    echo Removing gameserver build directory...
    rmdir /s /q "project\gameserver\source\build_win64"
)

if exist "project\central\source\build_win64" (
    echo Removing central build directory...
    rmdir /s /q "project\central\source\build_win64"
)

if exist "project\placeroute\source\build_win64" (
    echo Removing placeroute build directory...
    rmdir /s /q "project\placeroute\source\build_win64"
)

if exist "project\daemon\source\build_win64" (
    echo Removing daemon build directory...
    rmdir /s /q "project\daemon\source\build_win64"
)

if exist "project\chat\source\build_win64" (
    echo Removing chat build directory...
    rmdir /s /q "project\chat\source\build_win64"
)

if exist "project\background\source\build_win64" (
    echo Removing background build directory...
    rmdir /s /q "project\background\source\build_win64"
)

if exist "project\log\source\build_win64" (
    echo Removing log build directory...
    rmdir /s /q "project\log\source\build_win64"
)

if exist "project\sdk\source\build_win64" (
    echo Removing sdk build directory...
    rmdir /s /q "project\sdk\source\build_win64"
)

if exist "project\errorlog\source\build_win64" (
    echo Removing errorlog build directory...
    rmdir /s /q "project\errorlog\source\build_win64"
)

if exist "project\rdzone\source\build_win64" (
    echo Removing rdzone build directory...
    rmdir /s /q "project\rdzone\source\build_win64"
)

if exist "project\team\source\build_win64" (
    echo Removing team build directory...
    rmdir /s /q "project\team\source\build_win64"
)

echo Build cache cleaned successfully!
echo You can now run the build scripts.
